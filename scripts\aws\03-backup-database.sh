#!/bin/bash

# TuChanga - Database Backup Script
# Creates a backup of Aurora PostgreSQL and sends it via email

set -e

# Configuration
AWS_REGION="sa-east-1"
PROJECT_NAME="tuchanga"
BACKUP_BUCKET="$PROJECT_NAME-backups-$(date +%Y%m%d)"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to show usage
show_usage() {
    echo "Usage: $0 [<EMAIL>]"
    echo ""
    echo "Creates a backup of TuChanga Aurora database and sends it via email"
    echo ""
    echo "Options:"
    echo "  <EMAIL>    Email address to send backup (optional, will prompt if not provided)"
    echo "  -h, --help          Show this help message"
    echo ""
    echo "Examples:"
    echo "  $0 <EMAIL>"
    echo "  $0  # Will prompt for email"
}

# Parse arguments
EMAIL=""
if [ "$1" = "-h" ] || [ "$1" = "--help" ]; then
    show_usage
    exit 0
elif [ -n "$1" ]; then
    EMAIL="$1"
fi

# Prompt for email if not provided
if [ -z "$EMAIL" ]; then
    echo -e "${YELLOW}📧 Enter email address to send backup:${NC}"
    read -p "Email: " EMAIL
    
    if [ -z "$EMAIL" ]; then
        echo -e "${RED}❌ Email address is required${NC}"
        exit 1
    fi
fi

# Validate email format
if ! echo "$EMAIL" | grep -qE '^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Za-z]{2,}$'; then
    echo -e "${RED}❌ Invalid email format: $EMAIL${NC}"
    exit 1
fi

echo -e "${GREEN}🗄️  Starting TuChanga Database Backup${NC}"
echo -e "${BLUE}📧 Backup will be sent to: $EMAIL${NC}"

# Check if Aurora cluster exists
CLUSTER_ID="$PROJECT_NAME-aurora-cluster"
AURORA_ENDPOINT=$(aws rds describe-db-clusters --db-cluster-identifier $CLUSTER_ID --query 'DBClusters[0].Endpoint' --output text 2>/dev/null)

if [ -z "$AURORA_ENDPOINT" ] || [ "$AURORA_ENDPOINT" = "None" ]; then
    echo -e "${RED}❌ Aurora cluster not found: $CLUSTER_ID${NC}"
    echo "Make sure you've run the deployment script first."
    exit 1
fi

# Get database credentials from Secrets Manager
echo -e "${YELLOW}🔐 Retrieving database credentials...${NC}"
DB_PASSWORD=$(aws secretsmanager get-secret-value --secret-id "$PROJECT_NAME/db-password" --query 'SecretString' --output text)

if [ -z "$DB_PASSWORD" ]; then
    echo -e "${RED}❌ Could not retrieve database password from Secrets Manager${NC}"
    exit 1
fi

# Create timestamp for backup
TIMESTAMP=$(date +"%Y%m%d_%H%M%S")
BACKUP_FILENAME="tuchanga_backup_$TIMESTAMP.sql"
BACKUP_PATH="/tmp/$BACKUP_FILENAME"

# Create Aurora snapshot
echo -e "${YELLOW}📸 Creating Aurora snapshot...${NC}"
SNAPSHOT_ID="$PROJECT_NAME-manual-snapshot-$TIMESTAMP"

aws rds create-db-cluster-snapshot \
    --db-cluster-identifier $CLUSTER_ID \
    --db-cluster-snapshot-identifier $SNAPSHOT_ID

echo "Waiting for snapshot to complete..."
aws rds wait db-cluster-snapshot-completed --db-cluster-snapshot-identifier $SNAPSHOT_ID

echo -e "${GREEN}✅ Aurora snapshot created: $SNAPSHOT_ID${NC}"

# Create logical backup using pg_dump
echo -e "${YELLOW}💾 Creating logical backup with pg_dump...${NC}"

# Check if we can connect to the database
if ! command -v pg_dump >/dev/null 2>&1; then
    echo -e "${YELLOW}⚠️  pg_dump not found locally. Installing PostgreSQL client...${NC}"
    
    # Install PostgreSQL client based on OS
    if command -v yum >/dev/null 2>&1; then
        # Amazon Linux / CentOS / RHEL
        sudo yum install -y postgresql15
    elif command -v apt-get >/dev/null 2>&1; then
        # Ubuntu / Debian
        sudo apt-get update && sudo apt-get install -y postgresql-client-15
    elif command -v brew >/dev/null 2>&1; then
        # macOS
        brew install postgresql@15
    else
        echo -e "${RED}❌ Could not install PostgreSQL client. Please install pg_dump manually.${NC}"
        exit 1
    fi
fi

# Create logical backup
echo "Creating logical backup..."
export PGPASSWORD="$DB_PASSWORD"

pg_dump \
    --host="$AURORA_ENDPOINT" \
    --port=5432 \
    --username=postgres \
    --dbname=tuchanga \
    --verbose \
    --clean \
    --if-exists \
    --create \
    --format=plain \
    --file="$BACKUP_PATH"

# Compress the backup
echo -e "${YELLOW}🗜️  Compressing backup...${NC}"
gzip "$BACKUP_PATH"
BACKUP_PATH="$BACKUP_PATH.gz"

# Get backup size
BACKUP_SIZE=$(du -h "$BACKUP_PATH" | cut -f1)
echo -e "${GREEN}✅ Backup created: $BACKUP_FILENAME.gz ($BACKUP_SIZE)${NC}"

# Create S3 bucket for backups if it doesn't exist
echo -e "${YELLOW}☁️  Uploading backup to S3...${NC}"

aws s3 mb s3://$BACKUP_BUCKET --region $AWS_REGION 2>/dev/null || true

# Upload to S3
S3_KEY="backups/$BACKUP_FILENAME.gz"
aws s3 cp "$BACKUP_PATH" "s3://$BACKUP_BUCKET/$S3_KEY"

# Generate presigned URL for download (valid for 7 days)
DOWNLOAD_URL=$(aws s3 presign "s3://$BACKUP_BUCKET/$S3_KEY" --expires-in 604800)

echo -e "${GREEN}✅ Backup uploaded to S3${NC}"

# Send email with backup information
echo -e "${YELLOW}📧 Sending backup notification email...${NC}"

# Create email content
EMAIL_SUBJECT="TuChanga Database Backup - $TIMESTAMP"
EMAIL_BODY="TuChanga Database Backup Report

Backup Details:
- Date: $(date)
- Database: tuchanga
- Aurora Cluster: $CLUSTER_ID
- Backup Size: $BACKUP_SIZE
- Snapshot ID: $SNAPSHOT_ID

Files Created:
1. Aurora Snapshot: $SNAPSHOT_ID (Available in AWS RDS Console)
2. Logical Backup: $BACKUP_FILENAME.gz (Available via download link below)

Download Link (Valid for 7 days):
$DOWNLOAD_URL

Backup Location:
S3 Bucket: $BACKUP_BUCKET
S3 Key: $S3_KEY

This backup includes:
- Complete database schema
- All table data
- Indexes and constraints
- User permissions

To restore from logical backup:
1. Download the file using the link above
2. Uncompress: gunzip $BACKUP_FILENAME.gz
3. Restore: psql -h [host] -U postgres -d tuchanga < $BACKUP_FILENAME

To restore from Aurora snapshot:
1. Go to AWS RDS Console
2. Select Snapshots
3. Find snapshot: $SNAPSHOT_ID
4. Click 'Restore Snapshot'

---
TuChanga Backup System
Generated on $(date)
"

# Send email using AWS SES
aws ses send-email \
    --source "<EMAIL>" \
    --destination "ToAddresses=$EMAIL" \
    --message "Subject={Data=\"$EMAIL_SUBJECT\",Charset=utf-8},Body={Text={Data=\"$EMAIL_BODY\",Charset=utf-8}}" \
    --region $AWS_REGION 2>/dev/null || {
    
    echo -e "${YELLOW}⚠️  Could not send email via SES. Backup details:${NC}"
    echo ""
    echo -e "${BLUE}📋 Backup Summary:${NC}"
    echo "Aurora Snapshot: $SNAPSHOT_ID"
    echo "Logical Backup: $BACKUP_FILENAME.gz"
    echo "S3 Location: s3://$BACKUP_BUCKET/$S3_KEY"
    echo "Download URL: $DOWNLOAD_URL"
    echo ""
    echo "Note: Configure AWS SES to enable email notifications."
}

# Clean up local backup file
rm -f "$BACKUP_PATH"

echo -e "${GREEN}🎉 Database backup completed successfully!${NC}"
echo -e "${BLUE}📋 Summary:${NC}"
echo "Aurora Snapshot: $SNAPSHOT_ID"
echo "Logical Backup: s3://$BACKUP_BUCKET/$S3_KEY"
echo "Email sent to: $EMAIL"
echo ""
echo -e "${YELLOW}💡 Tip: Aurora snapshots are automatically retained according to your backup retention policy.${NC}"
