-- Initialize the job platform database
-- This script runs when the PostgreSQL container starts for the first time

-- Create extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pg_trgm";

-- Create indexes for better performance
-- These will be created by TypeORM migrations, but we can prepare the database

-- Grant permissions
GRANT ALL PRIVILEGES ON DATABASE tuchanga TO postgres;

-- Create schemas if needed
-- CREATE SCHEMA IF NOT EXISTS job_platform;

-- Log initialization
DO $$
BEGIN
    RAISE NOTICE 'Job Platform database initialized successfully';
END $$;
