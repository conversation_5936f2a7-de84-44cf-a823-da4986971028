{"family": "tuchanga-backend", "networkMode": "awsvpc", "requiresCompatibilities": ["FARGATE"], "cpu": "256", "memory": "512", "executionRoleArn": "arn:aws:iam::ACCOUNT_ID:role/ecsTaskExecutionRole", "taskRoleArn": "arn:aws:iam::ACCOUNT_ID:role/tuchanga-task-role", "containerDefinitions": [{"name": "tuchanga-backend", "image": "ACCOUNT_ID.dkr.ecr.sa-east-1.amazonaws.com/tuchanga-backend:latest", "portMappings": [{"containerPort": 3000, "protocol": "tcp"}], "essential": true, "environment": [{"name": "NODE_ENV", "value": "production"}, {"name": "HOST", "value": "0.0.0.0"}, {"name": "PORT", "value": "3000"}, {"name": "DB_HOST", "value": "tuchanga-aurora-cluster.cluster-xxxxx.sa-east-1.rds.amazonaws.com"}, {"name": "DB_PORT", "value": "5432"}, {"name": "DB_USERNAME", "value": "postgres"}, {"name": "DB_DATABASE", "value": "tuchanga"}, {"name": "JWT_EXPIRES_IN", "value": "24h"}, {"name": "FIREBASE_PROJECT_ID", "value": "tu-changa-583b3"}, {"name": "CORS_ORIGIN", "value": "*"}, {"name": "API_PREFIX", "value": ""}], "secrets": [{"name": "DB_PASSWORD", "valueFrom": "arn:aws:secretsmanager:sa-east-1:ACCOUNT_ID:secret:tuchanga/db-password"}, {"name": "JWT_SECRET", "valueFrom": "arn:aws:secretsmanager:sa-east-1:ACCOUNT_ID:secret:tuchanga/jwt-secret"}, {"name": "DATABASE_URL", "valueFrom": "arn:aws:secretsmanager:sa-east-1:ACCOUNT_ID:secret:tuchanga/database-url"}], "logConfiguration": {"logDriver": "awslogs", "options": {"awslogs-group": "/ecs/tuchanga-backend", "awslogs-region": "sa-east-1", "awslogs-stream-prefix": "ecs"}}, "healthCheck": {"command": ["CMD-SHELL", "curl -f http://localhost:3000/health || exit 1"], "interval": 30, "timeout": 5, "retries": 3, "startPeriod": 60}}]}