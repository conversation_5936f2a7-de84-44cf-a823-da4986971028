#!/bin/bash

# TuChanga AWS Management - Main Interactive Script
# Central hub for all AWS deployment and management operations

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
PURPLE='\033[0;35m'
NC='\033[0m' # No Color

# Configuration
PROJECT_NAME="tuchanga"
AWS_REGION="sa-east-1"

# Function to display banner
show_banner() {
    echo -e "${CYAN}"
    echo "╔══════════════════════════════════════════════════════════════╗"
    echo "║                                                              ║"
    echo "║                    🚀 TuChanga AWS Manager                   ║"
    echo "║                                                              ║"
    echo "║              Complete AWS Deployment & Management            ║"
    echo "║                                                              ║"
    echo "╚══════════════════════════════════════════════════════════════╝"
    echo -e "${NC}"
}

# Function to check prerequisites
check_prerequisites() {
    echo -e "${YELLOW}🔍 Checking prerequisites...${NC}"
    
    local missing_tools=()
    
    # Check AWS CLI
    if ! command -v aws >/dev/null 2>&1; then
        missing_tools+=("aws-cli")
    fi
    
    # Check Docker
    if ! command -v docker >/dev/null 2>&1; then
        missing_tools+=("docker")
    fi
    
    # Check if AWS is configured
    if ! aws sts get-caller-identity >/dev/null 2>&1; then
        echo -e "${RED}❌ AWS CLI not configured or no valid credentials${NC}"
        echo "Please run: aws configure"
        return 1
    fi
    
    if [ ${#missing_tools[@]} -ne 0 ]; then
        echo -e "${RED}❌ Missing required tools: ${missing_tools[*]}${NC}"
        echo "Please install the missing tools and try again."
        return 1
    fi
    
    # Get AWS account info
    local aws_account=$(aws sts get-caller-identity --query Account --output text)
    local aws_user=$(aws sts get-caller-identity --query Arn --output text | cut -d'/' -f2)
    
    echo -e "${GREEN}✅ Prerequisites check passed${NC}"
    echo -e "${BLUE}📋 AWS Account: $aws_account${NC}"
    echo -e "${BLUE}👤 AWS User: $aws_user${NC}"
    echo -e "${BLUE}🌍 Region: $AWS_REGION${NC}"
    echo ""
}

# Function to check current deployment status
check_deployment_status() {
    echo -e "${YELLOW}📊 Checking current deployment status...${NC}"
    
    # Check VPC
    local vpc_id=$(aws ec2 describe-vpcs --filters "Name=tag:Name,Values=$PROJECT_NAME-vpc" --query 'Vpcs[0].VpcId' --output text 2>/dev/null)
    if [ "$vpc_id" != "None" ] && [ -n "$vpc_id" ]; then
        echo -e "${GREEN}✅ VPC exists: $vpc_id${NC}"
    else
        echo -e "${RED}❌ VPC not found${NC}"
    fi
    
    # Check Aurora cluster
    local aurora_status=$(aws rds describe-db-clusters --db-cluster-identifier $PROJECT_NAME-aurora-cluster --query 'DBClusters[0].Status' --output text 2>/dev/null)
    if [ "$aurora_status" != "None" ] && [ -n "$aurora_status" ]; then
        echo -e "${GREEN}✅ Aurora cluster: $aurora_status${NC}"
    else
        # Check RDS instance (minimal cost option)
        local rds_status=$(aws rds describe-db-instances --db-instance-identifier $PROJECT_NAME-postgres --query 'DBInstances[0].DBInstanceStatus' --output text 2>/dev/null)
        if [ "$rds_status" != "None" ] && [ -n "$rds_status" ]; then
            echo -e "${GREEN}✅ RDS PostgreSQL: $rds_status (MINIMAL COST)${NC}"
        else
            echo -e "${RED}❌ No database found (Aurora or RDS)${NC}"
        fi
    fi
    
    # Check ECS cluster
    local ecs_status=$(aws ecs describe-clusters --clusters $PROJECT_NAME-cluster --query 'clusters[0].status' --output text 2>/dev/null)
    if [ "$ecs_status" = "ACTIVE" ]; then
        echo -e "${GREEN}✅ ECS cluster: $ecs_status${NC}"
        
        # Check services
        local backend_service=$(aws ecs describe-services --cluster $PROJECT_NAME-cluster --services $PROJECT_NAME-backend-service --query 'services[0].status' --output text 2>/dev/null)
        local frontend_service=$(aws ecs describe-services --cluster $PROJECT_NAME-cluster --services $PROJECT_NAME-frontend-service --query 'services[0].status' --output text 2>/dev/null)
        
        if [ "$backend_service" = "ACTIVE" ]; then
            echo -e "${GREEN}✅ Backend service: $backend_service${NC}"
        else
            echo -e "${RED}❌ Backend service not active${NC}"
        fi
        
        if [ "$frontend_service" = "ACTIVE" ]; then
            echo -e "${GREEN}✅ Frontend service: $frontend_service${NC}"
        else
            echo -e "${RED}❌ Frontend service not active${NC}"
        fi
    else
        echo -e "${RED}❌ ECS cluster not found or inactive${NC}"
    fi
    
    # Check ALB
    local alb_dns=$(aws elbv2 describe-load-balancers --query "LoadBalancers[?contains(LoadBalancerName, '$PROJECT_NAME')].DNSName" --output text 2>/dev/null)
    if [ -n "$alb_dns" ] && [ "$alb_dns" != "None" ]; then
        echo -e "${GREEN}✅ Load Balancer: $alb_dns${NC}"
    else
        echo -e "${RED}❌ Load Balancer not found${NC}"
    fi
    
    echo ""
}

# Function to show main menu
show_main_menu() {
    echo -e "${CYAN}🎯 What would you like to do?${NC}"
    echo ""
    echo -e "${YELLOW}📦 DEPLOYMENT${NC}"
    echo "  1) 🚀 Full deployment from scratch (VPC, Aurora, ECS, ALB) - ~$85/mes"
    echo "  2) 💰 MINIMAL COST deployment (VPC, RDS, ECS, ALB) - ~$35/mes"
    echo "  3) 🐳 Deploy containers only (Backend + Frontend)"
    echo "  4) 🔄 Update deployment (Backend/Frontend/Both)"
    echo ""
    echo -e "${YELLOW}🗄️  DATABASE${NC}"
    echo "  5) 💾 Create database backup and send via email"
    echo "  6) 📊 Check database status and connection"
    echo ""
    echo -e "${YELLOW}🔧 MANAGEMENT${NC}"
    echo "  7) 📋 View deployment status"
    echo "  8) 📊 View CloudWatch logs"
    echo "  9) 🗑️  Clean up resources (DANGER)"
    echo ""
    echo -e "${YELLOW}📚 HELP${NC}"
    echo " 10) 📖 View AWS permissions required"
    echo " 11) 💰 Cost optimization guide"
    echo " 12) 🆘 Troubleshooting guide"
    echo ""
    echo -e "${YELLOW}❌ EXIT${NC}"
    echo "  0) Exit"
    echo ""
    echo -e "${BLUE}═══════════════════════════════════════════════════════════════${NC}"
}

# Function to execute choice
execute_choice() {
    local choice=$1
    
    case $choice in
        1)
            echo -e "${GREEN}🚀 Starting full deployment from scratch (Aurora)...${NC}"
            echo -e "${YELLOW}⚠️  This will create all AWS resources (~$85/mes). Continue? (y/N)${NC}"
            read -p "Confirm: " confirm
            if [[ $confirm =~ ^[Yy]$ ]]; then
                ./01-full-deploy.sh
            else
                echo "Cancelled."
            fi
            ;;
        2)
            echo -e "${GREEN}💰 Starting MINIMAL COST deployment (RDS)...${NC}"
            echo -e "${YELLOW}⚠️  This will create AWS resources optimized for minimal cost (~$35/mes). Continue? (y/N)${NC}"
            read -p "Confirm: " confirm
            if [[ $confirm =~ ^[Yy]$ ]]; then
                ./01-full-deploy-minimal.sh
            else
                echo "Cancelled."
            fi
            ;;
        3)
            echo -e "${GREEN}🐳 Deploying containers...${NC}"
            ./02-deploy-containers.sh
            ;;
        4)
            echo -e "${GREEN}🔄 Updating deployment...${NC}"
            ./04-update-deployment.sh
            ;;
        5)
            echo -e "${GREEN}💾 Creating database backup...${NC}"
            ./03-backup-database.sh
            ;;
        6)
            echo -e "${GREEN}📊 Checking database status...${NC}"
            check_database_status
            ;;
        7)
            echo -e "${GREEN}📋 Checking deployment status...${NC}"
            check_deployment_status
            ;;
        8)
            echo -e "${GREEN}📊 Viewing CloudWatch logs...${NC}"
            view_logs
            ;;
        9)
            echo -e "${RED}🗑️  WARNING: This will delete ALL TuChanga resources!${NC}"
            echo -e "${YELLOW}⚠️  Are you absolutely sure? Type 'DELETE' to confirm:${NC}"
            read -p "Confirm: " confirm
            if [ "$confirm" = "DELETE" ]; then
                cleanup_resources
            else
                echo "Cancelled."
            fi
            ;;
        10)
            echo -e "${GREEN}📖 Viewing AWS permissions...${NC}"
            cat AWS_PERMISSIONS_REQUIRED.md
            ;;
        11)
            echo -e "${GREEN}💰 Cost optimization guide...${NC}"
            cat COST_OPTIMIZATION_GUIDE.md
            ;;
        12)
            echo -e "${GREEN}🆘 Troubleshooting guide...${NC}"
            show_troubleshooting
            ;;
        0)
            echo -e "${GREEN}👋 Goodbye!${NC}"
            exit 0
            ;;
        *)
            echo -e "${RED}❌ Invalid choice: $choice${NC}"
            ;;
    esac
}

# Function to check database status
check_database_status() {
    echo -e "${YELLOW}🗄️  Checking database status...${NC}"

    # Check Aurora cluster first
    local cluster_id="$PROJECT_NAME-aurora-cluster"
    local cluster_status=$(aws rds describe-db-clusters --db-cluster-identifier $cluster_id --query 'DBClusters[0].Status' --output text 2>/dev/null)

    if [ "$cluster_status" != "None" ] && [ -n "$cluster_status" ]; then
        local endpoint=$(aws rds describe-db-clusters --db-cluster-identifier $cluster_id --query 'DBClusters[0].Endpoint' --output text)
        local engine_version=$(aws rds describe-db-clusters --db-cluster-identifier $cluster_id --query 'DBClusters[0].EngineVersion' --output text)

        echo -e "${GREEN}✅ Aurora Cluster Status: $cluster_status${NC}"
        echo -e "${BLUE}📍 Endpoint: $endpoint${NC}"
        echo -e "${BLUE}🔧 Engine Version: $engine_version${NC}"

        # Test connection
        echo -e "${YELLOW}🔌 Testing database connection...${NC}"
        local db_password=$(aws secretsmanager get-secret-value --secret-id "$PROJECT_NAME/db-password" --query 'SecretString' --output text 2>/dev/null)

        if [ -n "$db_password" ]; then
            echo "Connection test would require pg_isready or psql client."
            echo "Endpoint: $endpoint:5432"
            echo "Database: tuchanga"
            echo "Username: postgres"
        else
            echo -e "${RED}❌ Could not retrieve database password${NC}"
        fi
    else
        # Check RDS instance (minimal cost option)
        local instance_id="$PROJECT_NAME-postgres"
        local instance_status=$(aws rds describe-db-instances --db-instance-identifier $instance_id --query 'DBInstances[0].DBInstanceStatus' --output text 2>/dev/null)

        if [ "$instance_status" != "None" ] && [ -n "$instance_status" ]; then
            local endpoint=$(aws rds describe-db-instances --db-instance-identifier $instance_id --query 'DBInstances[0].Endpoint.Address' --output text)
            local engine_version=$(aws rds describe-db-instances --db-instance-identifier $instance_id --query 'DBInstances[0].EngineVersion' --output text)
            local instance_class=$(aws rds describe-db-instances --db-instance-identifier $instance_id --query 'DBInstances[0].DBInstanceClass' --output text)

            echo -e "${GREEN}✅ RDS PostgreSQL Status: $instance_status (MINIMAL COST)${NC}"
            echo -e "${BLUE}📍 Endpoint: $endpoint${NC}"
            echo -e "${BLUE}🔧 Engine Version: $engine_version${NC}"
            echo -e "${BLUE}💰 Instance Class: $instance_class${NC}"

            # Test connection
            echo -e "${YELLOW}🔌 Testing database connection...${NC}"
            local db_password=$(aws secretsmanager get-secret-value --secret-id "$PROJECT_NAME/db-password" --query 'SecretString' --output text 2>/dev/null)

            if [ -n "$db_password" ]; then
                echo "Connection test would require pg_isready or psql client."
                echo "Endpoint: $endpoint:5432"
                echo "Database: tuchanga"
                echo "Username: postgres"
            else
                echo -e "${RED}❌ Could not retrieve database password${NC}"
            fi
        else
            echo -e "${RED}❌ No database found (Aurora or RDS)${NC}"
        fi
    fi
}

# Function to view logs
view_logs() {
    echo -e "${YELLOW}📊 Available log groups:${NC}"
    echo "1) Backend logs"
    echo "2) Frontend logs"
    echo "3) Both"
    echo ""
    read -p "Choose logs to view (1-3): " log_choice
    
    case $log_choice in
        1)
            echo -e "${GREEN}📋 Backend logs (last 50 lines):${NC}"
            aws logs tail "/ecs/$PROJECT_NAME-backend" --follow --since 1h
            ;;
        2)
            echo -e "${GREEN}📋 Frontend logs (last 50 lines):${NC}"
            aws logs tail "/ecs/$PROJECT_NAME-frontend" --follow --since 1h
            ;;
        3)
            echo -e "${GREEN}📋 All logs (last 50 lines each):${NC}"
            echo "Backend logs:"
            aws logs tail "/ecs/$PROJECT_NAME-backend" --since 1h | head -50
            echo ""
            echo "Frontend logs:"
            aws logs tail "/ecs/$PROJECT_NAME-frontend" --since 1h | head -50
            ;;
        *)
            echo -e "${RED}❌ Invalid choice${NC}"
            ;;
    esac
}

# Function to show troubleshooting guide
show_troubleshooting() {
    echo -e "${CYAN}🆘 TuChanga Troubleshooting Guide${NC}"
    echo ""
    echo -e "${YELLOW}Common Issues:${NC}"
    echo ""
    echo "1. 🔐 Permission Denied"
    echo "   - Check AWS permissions (option 9)"
    echo "   - Verify AWS CLI configuration: aws sts get-caller-identity"
    echo ""
    echo "2. 🐳 Container Build Failures"
    echo "   - Check Docker is running: docker ps"
    echo "   - Verify ECR login: aws ecr get-login-password"
    echo ""
    echo "3. 🗄️  Database Connection Issues"
    echo "   - Check security groups allow port 5432"
    echo "   - Verify Aurora cluster is 'available'"
    echo "   - Check secrets in Secrets Manager"
    echo ""
    echo "4. 🌐 Application Not Accessible"
    echo "   - Check ALB health checks"
    echo "   - Verify target groups have healthy targets"
    echo "   - Check security group rules"
    echo ""
    echo "5. 📊 Service Not Starting"
    echo "   - Check CloudWatch logs (option 7)"
    echo "   - Verify task definition is valid"
    echo "   - Check resource limits (CPU/Memory)"
    echo ""
    echo -e "${BLUE}For more help, check CloudWatch logs and AWS Console.${NC}"
}

# Function to cleanup resources
cleanup_resources() {
    echo -e "${RED}🗑️  Starting resource cleanup...${NC}"
    echo "This will delete (in order):"
    echo "1. ECS Services and Tasks"
    echo "2. Load Balancer and Target Groups"
    echo "3. Aurora Database Cluster"
    echo "4. VPC and Networking"
    echo "5. ECR Repositories"
    echo "6. IAM Roles"
    echo "7. Secrets Manager secrets"
    echo ""
    echo -e "${YELLOW}⚠️  This action cannot be undone!${NC}"
    echo -e "${YELLOW}⚠️  All data will be permanently lost!${NC}"
    echo ""
    read -p "Type 'I UNDERSTAND' to proceed: " final_confirm
    
    if [ "$final_confirm" = "I UNDERSTAND" ]; then
        echo -e "${RED}🔥 Proceeding with cleanup...${NC}"
        # Add cleanup logic here
        echo "Cleanup functionality would be implemented here."
        echo "For safety, this is not implemented in the demo."
    else
        echo "Cleanup cancelled."
    fi
}

# Main execution
main() {
    # Change to script directory
    cd "$(dirname "$0")"
    
    # Show banner
    show_banner
    
    # Check prerequisites
    if ! check_prerequisites; then
        exit 1
    fi
    
    # Check current status
    check_deployment_status
    
    # Main loop
    while true; do
        show_main_menu
        read -p "Enter your choice (0-10): " choice
        echo ""
        
        execute_choice "$choice"
        
        echo ""
        echo -e "${BLUE}═══════════════════════════════════════════════════════════════${NC}"
        read -p "Press Enter to continue..."
        clear
        show_banner
    done
}

# Run main function
main "$@"
