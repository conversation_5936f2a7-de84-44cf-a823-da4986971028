#!/bin/bash

# Setup Nginx configuration for Tuchanga
# This script configures Nginx as reverse proxy with SSL

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to check if command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Configuration
DOMAIN="$1"
PROJECT_DIR="/opt/tuchanga"
SSL_DIR="$PROJECT_DIR/ssl"
NGINX_SITES_AVAILABLE="/etc/nginx/sites-available"
NGINX_SITES_ENABLED="/etc/nginx/sites-enabled"
NGINX_CONFIG_FILE="$NGINX_SITES_AVAILABLE/tuchanga"

# Validate inputs
if [[ -z "$DOMAIN" ]]; then
    print_error "Dominio es requerido"
    echo "Uso: $0 <domain>"
    echo "Ejemplo: $0 midominio.com"
    exit 1
fi

# Check prerequisites
check_prerequisites() {
    if ! command_exists nginx; then
        print_error "Nginx no está instalado. Ejecuta install-dependencies.sh primero"
        exit 1
    fi
    
    if [[ ! -d "$PROJECT_DIR" ]]; then
        print_error "Directorio del proyecto no encontrado: $PROJECT_DIR"
        print_error "Ejecuta setup-repository.sh primero"
        exit 1
    fi
    
    if [[ ! -d "$SSL_DIR" ]] || [[ ! -f "$SSL_DIR/cert.pem" ]] || [[ ! -f "$SSL_DIR/key.pem" ]]; then
        print_error "Certificados SSL no encontrados. Ejecuta setup-ssl.sh primero"
        exit 1
    fi
}

# Backup existing Nginx configuration
backup_nginx_config() {
    if [[ -f "$NGINX_CONFIG_FILE" ]]; then
        local backup_file="$NGINX_CONFIG_FILE.backup.$(date +%Y%m%d-%H%M%S)"
        print_status "Creando backup de configuración existente..."
        sudo cp "$NGINX_CONFIG_FILE" "$backup_file"
        print_success "Backup creado: $backup_file"
    fi
}

# Create Nginx configuration
create_nginx_config() {
    print_status "Creando configuración de Nginx para $DOMAIN..."
    
    sudo tee "$NGINX_CONFIG_FILE" > /dev/null << EOF
# Tuchanga Nginx Configuration
# Generated automatically by setup script

# Rate limiting
limit_req_zone \$binary_remote_addr zone=api:10m rate=10r/s;
limit_req_zone \$binary_remote_addr zone=general:10m rate=30r/s;

# Upstream servers
upstream backend {
    server 127.0.0.1:3000;
    keepalive 32;
}

# HTTP to HTTPS redirect
server {
    listen 80;
    listen [::]:80;
    server_name $DOMAIN www.$DOMAIN api.$DOMAIN;
    
    # Let's Encrypt challenge
    location /.well-known/acme-challenge/ {
        root /var/www/html;
    }
    
    # Redirect all other traffic to HTTPS
    location / {
        return 301 https://\$server_name\$request_uri;
    }
}

# Main website (www.$DOMAIN and $DOMAIN)
server {
    listen 443 ssl http2;
    listen [::]:443 ssl http2;
    server_name $DOMAIN www.$DOMAIN;
    
    # SSL Configuration
    ssl_certificate $SSL_DIR/cert.pem;
    ssl_certificate_key $SSL_DIR/key.pem;
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES128-GCM-SHA256:ECDHE-RSA-AES256-GCM-SHA384:ECDHE-RSA-AES128-SHA256:ECDHE-RSA-AES256-SHA384;
    ssl_prefer_server_ciphers off;
    ssl_session_cache shared:SSL:10m;
    ssl_session_timeout 10m;
    
    # Security headers
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header Referrer-Policy "strict-origin-when-cross-origin" always;
    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always;
    
    # Rate limiting
    limit_req zone=general burst=50 nodelay;
    
    # Root directory for static files
    root /var/www/tuchanga;
    index index.html;
    
    # Gzip compression
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_types text/plain text/css text/xml text/javascript application/javascript application/xml+rss application/json;
    
    # Static files with caching
    location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
        try_files \$uri =404;
    }
    
    # API proxy to backend
    location /api/ {
        limit_req zone=api burst=20 nodelay;
        
        proxy_pass http://backend;
        proxy_http_version 1.1;
        proxy_set_header Upgrade \$http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \$scheme;
        proxy_cache_bypass \$http_upgrade;
        
        # Timeouts
        proxy_connect_timeout 60s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;
        
        # Buffer settings
        proxy_buffering on;
        proxy_buffer_size 128k;
        proxy_buffers 4 256k;
        proxy_busy_buffers_size 256k;
    }
    
    # WebSocket support for real-time features
    location /socket.io/ {
        proxy_pass http://backend;
        proxy_http_version 1.1;
        proxy_set_header Upgrade \$http_upgrade;
        proxy_set_header Connection "upgrade";
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \$scheme;
    }
    
    # Health check endpoint
    location /health {
        proxy_pass http://backend/health;
        access_log off;
    }
    
    # Frontend routes (SPA)
    location / {
        try_files \$uri \$uri/ /index.html;
    }
    
    # Error pages
    error_page 404 /index.html;
    error_page 500 502 503 504 /50x.html;
    location = /50x.html {
        root /var/www/html;
    }
}

# API subdomain (api.$DOMAIN)
server {
    listen 443 ssl http2;
    listen [::]:443 ssl http2;
    server_name api.$DOMAIN;
    
    # SSL Configuration
    ssl_certificate $SSL_DIR/cert.pem;
    ssl_certificate_key $SSL_DIR/key.pem;
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES128-GCM-SHA256:ECDHE-RSA-AES256-GCM-SHA384:ECDHE-RSA-AES128-SHA256:ECDHE-RSA-AES256-SHA384;
    ssl_prefer_server_ciphers off;
    ssl_session_cache shared:SSL:10m;
    ssl_session_timeout 10m;
    
    # Security headers
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header Referrer-Policy "strict-origin-when-cross-origin" always;
    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always;
    
    # CORS headers for API
    add_header Access-Control-Allow-Origin "https://$DOMAIN" always;
    add_header Access-Control-Allow-Methods "GET, POST, PUT, DELETE, OPTIONS" always;
    add_header Access-Control-Allow-Headers "Origin, X-Requested-With, Content-Type, Accept, Authorization" always;
    add_header Access-Control-Allow-Credentials "true" always;
    
    # Rate limiting for API
    limit_req zone=api burst=20 nodelay;
    
    # Handle preflight requests
    location / {
        if (\$request_method = 'OPTIONS') {
            add_header Access-Control-Allow-Origin "https://$DOMAIN";
            add_header Access-Control-Allow-Methods "GET, POST, PUT, DELETE, OPTIONS";
            add_header Access-Control-Allow-Headers "Origin, X-Requested-With, Content-Type, Accept, Authorization";
            add_header Access-Control-Allow-Credentials "true";
            add_header Content-Length 0;
            add_header Content-Type text/plain;
            return 204;
        }
        
        proxy_pass http://backend;
        proxy_http_version 1.1;
        proxy_set_header Upgrade \$http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \$scheme;
        proxy_cache_bypass \$http_upgrade;
        
        # Timeouts
        proxy_connect_timeout 60s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;
    }
    
    # WebSocket support
    location /socket.io/ {
        proxy_pass http://backend;
        proxy_http_version 1.1;
        proxy_set_header Upgrade \$http_upgrade;
        proxy_set_header Connection "upgrade";
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \$scheme;
    }
}
EOF
    
    print_success "Configuración de Nginx creada"
}

# Create web directory for static files
create_web_directory() {
    print_status "Creando directorio web para archivos estáticos..."
    
    local web_dir="/var/www/tuchanga"
    
    # Create directory
    sudo mkdir -p "$web_dir"
    sudo chown -R www-data:www-data "$web_dir"
    sudo chmod -R 755 "$web_dir"
    
    # Create placeholder index.html if it doesn't exist
    if [[ ! -f "$web_dir/index.html" ]]; then
        sudo tee "$web_dir/index.html" > /dev/null << EOF
<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Tuchanga - Cargando...</title>
    <style>
        body { font-family: Arial, sans-serif; text-align: center; margin-top: 50px; }
        .loading { color: #666; }
    </style>
</head>
<body>
    <h1>Tuchanga</h1>
    <p class="loading">Aplicación iniciando...</p>
    <p>Si ves este mensaje, Nginx está funcionando correctamente.</p>
</body>
</html>
EOF
        sudo chown www-data:www-data "$web_dir/index.html"
    fi
    
    print_success "Directorio web creado: $web_dir"
}

# Enable site configuration
enable_site() {
    print_status "Habilitando configuración del sitio..."
    
    # Remove default site if it exists
    if [[ -f "$NGINX_SITES_ENABLED/default" ]]; then
        print_status "Deshabilitando sitio por defecto..."
        sudo rm -f "$NGINX_SITES_ENABLED/default"
    fi
    
    # Enable Tuchanga site
    if [[ ! -L "$NGINX_SITES_ENABLED/tuchanga" ]]; then
        sudo ln -s "$NGINX_CONFIG_FILE" "$NGINX_SITES_ENABLED/tuchanga"
        print_success "Sitio Tuchanga habilitado"
    else
        print_success "Sitio Tuchanga ya está habilitado"
    fi
}

# Test Nginx configuration
test_nginx_config() {
    print_status "Probando configuración de Nginx..."
    
    if sudo nginx -t; then
        print_success "Configuración de Nginx es válida"
    else
        print_error "Error en la configuración de Nginx"
        exit 1
    fi
}

# Restart Nginx service
restart_nginx() {
    print_status "Reiniciando servicio Nginx..."
    
    sudo systemctl restart nginx
    
    if systemctl is-active --quiet nginx; then
        print_success "Nginx reiniciado exitosamente"
    else
        print_error "Error al reiniciar Nginx"
        exit 1
    fi
}

# Configure firewall for Nginx
configure_firewall() {
    print_status "Configurando firewall para Nginx..."
    
    # Allow Nginx Full (HTTP and HTTPS)
    sudo ufw allow 'Nginx Full'
    
    # Remove individual HTTP/HTTPS rules if they exist
    sudo ufw delete allow 80/tcp 2>/dev/null || true
    sudo ufw delete allow 443/tcp 2>/dev/null || true
    
    print_success "Firewall configurado para Nginx"
}

# Show Nginx status
show_nginx_status() {
    print_status "Estado de Nginx:"
    echo ""
    
    # Service status
    if systemctl is-active --quiet nginx; then
        echo "✅ Servicio: Activo"
    else
        echo "❌ Servicio: Inactivo"
    fi
    
    # Configuration test
    if sudo nginx -t >/dev/null 2>&1; then
        echo "✅ Configuración: Válida"
    else
        echo "❌ Configuración: Error"
    fi
    
    # Listening ports
    echo "🔌 Puertos en uso:"
    sudo netstat -tlnp | grep nginx | awk '{print "   " $4}' || echo "   No se pudo obtener información"
    
    # Sites enabled
    echo "🌐 Sitios habilitados:"
    ls -la "$NGINX_SITES_ENABLED/" | grep -v "^total" | awk '{print "   " $9}' || echo "   Ninguno"
    
    echo ""
    print_status "URLs disponibles:"
    echo "- https://$DOMAIN (Frontend)"
    echo "- https://www.$DOMAIN (Frontend)"
    echo "- https://api.$DOMAIN (API Backend)"
    echo "- https://$DOMAIN/api/ (API a través del frontend)"
    
    echo ""
}

# Main function
main() {
    print_status "Configurando Nginx para $DOMAIN..."
    echo ""
    
    check_prerequisites
    backup_nginx_config
    create_nginx_config
    create_web_directory
    enable_site
    test_nginx_config
    configure_firewall
    restart_nginx
    
    echo ""
    show_nginx_status
    
    print_success "¡Nginx configurado exitosamente!"
    echo ""
    print_status "Próximos pasos:"
    echo "1. Iniciar aplicación: ./start-application.sh"
    echo "2. Verificar estado: ./check-status.sh"
    echo ""
    print_warning "NOTA: Los archivos estáticos del frontend se copiarán cuando inicies la aplicación"
}

# Run main function
main "$@"
