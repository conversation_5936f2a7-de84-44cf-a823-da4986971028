#!/bin/bash

# Setup Tuchanga application with PM2
# This script builds and configures the application to run natively

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to check if command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_DIR="$(cd "$SCRIPT_DIR/../.." && pwd)"
WEB_DIR="/var/www/tuchanga"

print_status "Directorio del proyecto detectado: $PROJECT_DIR"

# Check prerequisites
check_prerequisites() {
    if ! command_exists node; then
        print_error "Node.js no está instalado. Ejecuta install-dependencies.sh primero"
        exit 1
    fi
    
    if ! command_exists npm; then
        print_error "npm no está instalado. Ejecuta install-dependencies.sh primero"
        exit 1
    fi
    
    if ! command_exists pm2; then
        print_error "PM2 no está instalado. Ejecuta install-dependencies.sh primero"
        exit 1
    fi
    
    if [[ ! -d "$PROJECT_DIR" ]]; then
        print_error "Directorio del proyecto no encontrado: $PROJECT_DIR"
        exit 1
    fi
    
    if [[ ! -d "$PROJECT_DIR/backend" ]]; then
        print_error "Directorio backend no encontrado: $PROJECT_DIR/backend"
        exit 1
    fi
    
    if [[ ! -d "$PROJECT_DIR/frontend" ]]; then
        print_error "Directorio frontend no encontrado: $PROJECT_DIR/frontend"
        exit 1
    fi
}

# Install backend dependencies
install_backend_dependencies() {
    print_status "Instalando dependencias del backend..."
    
    cd "$PROJECT_DIR/backend"
    
    if [[ ! -f "package.json" ]]; then
        print_error "package.json no encontrado en el backend"
        exit 1
    fi
    
    # Install dependencies
    npm ci --production
    
    print_success "Dependencias del backend instaladas"
}

# Install frontend dependencies and build
build_frontend() {
    print_status "Construyendo frontend..."
    
    cd "$PROJECT_DIR/frontend"
    
    if [[ ! -f "package.json" ]]; then
        print_error "package.json no encontrado en el frontend"
        exit 1
    fi
    
    # Install dependencies
    print_status "Instalando dependencias del frontend..."
    npm ci
    
    # Build for production
    print_status "Compilando frontend para producción..."
    npm run build
    
    if [[ ! -d "dist" ]]; then
        print_error "Error al compilar frontend - directorio dist no encontrado"
        exit 1
    fi
    
    print_success "Frontend compilado exitosamente"
}

# Deploy frontend to web directory
deploy_frontend() {
    print_status "Desplegando frontend a directorio web..."
    
    cd "$PROJECT_DIR/frontend"
    
    # Create web directory
    sudo mkdir -p "$WEB_DIR"
    
    # Copy built files
    sudo rm -rf "$WEB_DIR"/*
    sudo cp -r dist/* "$WEB_DIR/"
    
    # Set proper permissions
    sudo chown -R www-data:www-data "$WEB_DIR"
    sudo chmod -R 755 "$WEB_DIR"
    
    print_success "Frontend desplegado en $WEB_DIR"
}

# Create PM2 ecosystem file
create_pm2_config() {
    print_status "Creando configuración PM2..."
    
    cd "$PROJECT_DIR"
    
    cat > ecosystem.config.js << 'EOF'
module.exports = {
  apps: [
    {
      name: 'tuchanga-backend',
      script: './backend/dist/main.js',
      cwd: './backend',
      instances: 1,
      exec_mode: 'fork',
      env: {
        NODE_ENV: 'production',
        PORT: 3000
      },
      error_file: '/var/log/tuchanga/backend-error.log',
      out_file: '/var/log/tuchanga/backend-out.log',
      log_file: '/var/log/tuchanga/backend-combined.log',
      time: true,
      max_memory_restart: '500M',
      restart_delay: 4000,
      max_restarts: 10,
      min_uptime: '10s'
    }
  ]
};
EOF
    
    print_success "Configuración PM2 creada"
}

# Build backend
build_backend() {
    print_status "Construyendo backend..."
    
    cd "$PROJECT_DIR/backend"
    
    # Check if TypeScript build is needed
    if [[ -f "tsconfig.json" ]]; then
        print_status "Compilando TypeScript..."
        npm run build
        
        if [[ ! -d "dist" ]]; then
            print_error "Error al compilar backend - directorio dist no encontrado"
            exit 1
        fi
    fi
    
    print_success "Backend compilado exitosamente"
}

# Setup logging directory
setup_logging() {
    print_status "Configurando directorio de logs..."
    
    sudo mkdir -p /var/log/tuchanga
    sudo chown $USER:$USER /var/log/tuchanga
    sudo chmod 755 /var/log/tuchanga
    
    print_success "Directorio de logs configurado"
}

# Stop existing PM2 processes
stop_existing_processes() {
    print_status "Deteniendo procesos existentes..."
    
    # Stop and delete existing processes
    pm2 stop tuchanga-backend 2>/dev/null || true
    pm2 delete tuchanga-backend 2>/dev/null || true
    
    print_success "Procesos existentes detenidos"
}

# Start application with PM2
start_application() {
    print_status "Iniciando aplicación con PM2..."
    
    cd "$PROJECT_DIR"
    
    # Start application
    pm2 start ecosystem.config.js
    
    # Save PM2 configuration
    pm2 save
    
    # Wait for application to start
    sleep 5
    
    # Check if application is running
    if pm2 list | grep -q "tuchanga-backend.*online"; then
        print_success "Aplicación iniciada exitosamente"
    else
        print_error "Error al iniciar la aplicación"
        print_status "Mostrando logs de PM2:"
        pm2 logs tuchanga-backend --lines 10
        exit 1
    fi
}

# Test application health
test_application() {
    print_status "Probando salud de la aplicación..."
    
    local max_attempts=30
    local attempt=1
    
    while [[ $attempt -le $max_attempts ]]; do
        if curl -f http://localhost:3000/health >/dev/null 2>&1; then
            print_success "Aplicación responde correctamente"
            return 0
        fi
        
        print_status "Intento $attempt/$max_attempts - Esperando aplicación..."
        sleep 2
        ((attempt++))
    done
    
    print_error "La aplicación no responde después de $max_attempts intentos"
    print_status "Mostrando logs de PM2:"
    pm2 logs tuchanga-backend --lines 20
    return 1
}

# Show application status
show_application_status() {
    print_status "Estado de la aplicación:"
    echo ""
    
    # PM2 status
    pm2 list
    
    echo ""
    print_status "URLs de la aplicación:"
    echo "- Backend API: http://localhost:3000"
    echo "- Health check: http://localhost:3000/health"
    echo "- Frontend: Servido por Nginx"
    
    echo ""
    print_status "Comandos útiles:"
    echo "- Ver logs: pm2 logs tuchanga-backend"
    echo "- Reiniciar: pm2 restart tuchanga-backend"
    echo "- Detener: pm2 stop tuchanga-backend"
    echo "- Estado: pm2 status"
}

# Main function
main() {
    print_status "Configurando aplicación Tuchanga nativa..."
    echo ""
    
    check_prerequisites
    setup_logging
    stop_existing_processes
    install_backend_dependencies
    build_backend
    build_frontend
    deploy_frontend
    create_pm2_config
    start_application
    
    if test_application; then
        echo ""
        show_application_status
        
        print_success "¡Aplicación configurada y ejecutándose exitosamente!"
        echo ""
        print_status "Próximos pasos:"
        echo "1. Configurar Nginx: ./setup-nginx.sh"
        echo "2. Ver estado completo: ./check-status.sh"
    else
        print_error "La aplicación no está funcionando correctamente"
        exit 1
    fi
}

# Run main function
main "$@"
