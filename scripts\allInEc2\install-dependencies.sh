#!/bin/bash

# Install system dependencies for Tuchanga on Ubuntu 24 LTS
# This script installs all required packages and tools

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to check if command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Update system packages
update_system() {
    print_status "Actualizando paquetes del sistema..."
    
    if sudo apt update && sudo apt upgrade -y; then
        print_success "Sistema actualizado correctamente"
    else
        print_error "Error al actualizar el sistema"
        exit 1
    fi
}

# Install basic tools
install_basic_tools() {
    print_status "Instalando herramientas básicas..."
    
    local packages=(
        "curl"
        "wget"
        "git"
        "unzip"
        "software-properties-common"
        "apt-transport-https"
        "ca-certificates"
        "gnupg"
        "lsb-release"
        "build-essential"
        "openssl"
        "ufw"
        "htop"
        "nano"
        "vim"
        "tree"
        "jq"
        "net-tools"
    )
    
    for package in "${packages[@]}"; do
        if ! dpkg -l | grep -q "^ii  $package "; then
            print_status "Instalando $package..."
            sudo apt install -y "$package"
        else
            print_success "$package ya está instalado"
        fi
    done
}

# Install Node.js
install_nodejs() {
    print_status "Verificando instalación de Node.js..."
    
    if command_exists node && command_exists npm; then
        local node_version=$(node --version)
        print_success "Node.js ya está instalado: $node_version"
        return 0
    fi
    
    print_status "Instalando Node.js 20.x..."
    
    # Add NodeSource repository
    curl -fsSL https://deb.nodesource.com/setup_20.x | sudo -E bash -
    
    # Install Node.js
    sudo apt install -y nodejs
    
    # Verify installation
    if command_exists node && command_exists npm; then
        local node_version=$(node --version)
        local npm_version=$(npm --version)
        print_success "Node.js instalado correctamente: $node_version"
        print_success "npm instalado correctamente: $npm_version"
    else
        print_error "Error al instalar Node.js"
        exit 1
    fi
}

# Install PostgreSQL
install_postgresql() {
    print_status "Verificando instalación de PostgreSQL..."

    if command_exists psql && systemctl is-active --quiet postgresql; then
        print_success "PostgreSQL ya está instalado y ejecutándose"
        return 0
    fi

    print_status "Instalando PostgreSQL..."

    # Install PostgreSQL
    sudo apt install -y postgresql postgresql-contrib

    # Start and enable PostgreSQL
    sudo systemctl start postgresql
    sudo systemctl enable postgresql

    # Verify installation
    if command_exists psql && systemctl is-active --quiet postgresql; then
        local pg_version=$(sudo -u postgres psql -c "SELECT version();" | head -3 | tail -1)
        print_success "PostgreSQL instalado correctamente"
        print_status "Versión: $pg_version"
    else
        print_error "Error al instalar PostgreSQL"
        exit 1
    fi
}

# Install PM2 for process management
install_pm2() {
    print_status "Verificando instalación de PM2..."

    if command_exists pm2; then
        print_success "PM2 ya está instalado"
        return 0
    fi

    print_status "Instalando PM2..."

    # Install PM2 globally
    sudo npm install -g pm2

    # Setup PM2 startup script
    sudo pm2 startup systemd -u $USER --hp /home/<USER>

    # Verify installation
    if command_exists pm2; then
        local pm2_version=$(pm2 --version)
        print_success "PM2 instalado correctamente: v$pm2_version"
    else
        print_error "Error al instalar PM2"
        exit 1
    fi
}

# Install Nginx
install_nginx() {
    print_status "Verificando instalación de Nginx..."
    
    if command_exists nginx; then
        print_success "Nginx ya está instalado"
        return 0
    fi
    
    print_status "Instalando Nginx..."
    
    sudo apt install -y nginx
    
    # Start and enable Nginx
    sudo systemctl start nginx
    sudo systemctl enable nginx
    
    # Configure firewall
    sudo ufw allow 'Nginx Full'
    
    print_success "Nginx instalado y configurado correctamente"
}

# Install Certbot for SSL certificates
install_certbot() {
    print_status "Verificando instalación de Certbot..."
    
    if command_exists certbot; then
        print_success "Certbot ya está instalado"
        return 0
    fi
    
    print_status "Instalando Certbot..."
    
    sudo apt install -y certbot python3-certbot-nginx
    
    print_success "Certbot instalado correctamente"
}

# Configure firewall
configure_firewall() {
    print_status "Configurando firewall..."
    
    # Enable UFW if not already enabled
    if ! sudo ufw status | grep -q "Status: active"; then
        print_status "Habilitando UFW..."
        sudo ufw --force enable
    fi
    
    # Allow SSH
    sudo ufw allow ssh
    
    # Allow HTTP and HTTPS
    sudo ufw allow 80/tcp
    sudo ufw allow 443/tcp
    
    # Allow backend port (3000)
    sudo ufw allow 3000/tcp
    
    # Allow PostgreSQL (only from localhost)
    sudo ufw allow from 127.0.0.1 to any port 5432
    
    print_success "Firewall configurado correctamente"
}



# Main installation function
main() {
    print_status "Iniciando instalación de dependencias para Tuchanga..."
    echo ""
    
    # Check if running on Ubuntu 24
    if ! grep -q "Ubuntu 24" /etc/os-release; then
        print_warning "Este script está optimizado para Ubuntu 24 LTS"
        read -p "¿Continuar de todos modos? (y/n): " confirm
        if [[ $confirm != "y" ]]; then
            exit 1
        fi
    fi
    
    update_system
    install_basic_tools
    install_nodejs
    install_postgresql
    install_pm2
    install_nginx
    install_certbot
    configure_firewall
    
    echo ""
    print_success "¡Todas las dependencias han sido instaladas correctamente!"
    echo ""
    print_warning "IMPORTANTE: Si Docker fue instalado, necesitas cerrar sesión y volver a iniciar"
    print_warning "para poder usar Docker sin sudo, o ejecuta: newgrp docker"
    echo ""
    
    # Show installed versions
    echo "Versiones instaladas:"
    echo "- Node.js: $(node --version 2>/dev/null || echo 'No instalado')"
    echo "- npm: $(npm --version 2>/dev/null || echo 'No instalado')"
    echo "- PostgreSQL: $(sudo -u postgres psql -c 'SELECT version();' 2>/dev/null | head -3 | tail -1 | cut -d' ' -f1-2 || echo 'No instalado')"
    echo "- PM2: v$(pm2 --version 2>/dev/null || echo 'No instalado')"
    echo "- Nginx: $(nginx -v 2>&1 | cut -d' ' -f3 2>/dev/null || echo 'No instalado')"
    echo "- Certbot: $(certbot --version 2>/dev/null | cut -d' ' -f2 || echo 'No instalado')"
    echo ""
}

# Run main function
main "$@"
