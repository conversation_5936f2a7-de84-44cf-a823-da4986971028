#!/bin/bash

# TuChanga - Container Deployment Script
# Builds and deploys backend and frontend containers to ECS

set -e

# Configuration
AWS_REGION="sa-east-1"
PROJECT_NAME="tuchanga"
AWS_ACCOUNT_ID=$(aws sts get-caller-identity --query Account --output text)

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${GREEN}🚀 Starting TuChanga Container Deployment${NC}"

# Function to check if service exists
service_exists() {
    local cluster=$1
    local service=$2
    aws ecs describe-services --cluster $cluster --services $service --query 'services[0].serviceName' --output text 2>/dev/null | grep -v "None"
}

# Get Aurora endpoint
AURORA_ENDPOINT=$(aws rds describe-db-clusters --db-cluster-identifier $PROJECT_NAME-aurora-cluster --query 'DBClusters[0].Endpoint' --output text)
if [ -z "$AURORA_ENDPOINT" ] || [ "$AURORA_ENDPOINT" = "None" ]; then
    echo -e "${RED}❌ Aurora cluster not found. Run 01-full-deploy.sh first.${NC}"
    exit 1
fi

# Get VPC and subnet information
VPC_ID=$(aws ec2 describe-vpcs --filters "Name=tag:Name,Values=$PROJECT_NAME-vpc" --query 'Vpcs[0].VpcId' --output text)
PRIVATE_SUBNET_1_ID=$(aws ec2 describe-subnets --filters "Name=vpc-id,Values=$VPC_ID" "Name=tag:Name,Values=$PROJECT_NAME-private-1" --query 'Subnets[0].SubnetId' --output text)
PRIVATE_SUBNET_2_ID=$(aws ec2 describe-subnets --filters "Name=vpc-id,Values=$VPC_ID" "Name=tag:Name,Values=$PROJECT_NAME-private-2" --query 'Subnets[0].SubnetId' --output text)
PUBLIC_SUBNET_1_ID=$(aws ec2 describe-subnets --filters "Name=vpc-id,Values=$VPC_ID" "Name=tag:Name,Values=$PROJECT_NAME-public-1" --query 'Subnets[0].SubnetId' --output text)
PUBLIC_SUBNET_2_ID=$(aws ec2 describe-subnets --filters "Name=vpc-id,Values=$VPC_ID" "Name=tag:Name,Values=$PROJECT_NAME-public-2" --query 'Subnets[0].SubnetId' --output text)

# Get security groups
ECS_SG_ID=$(aws ec2 describe-security-groups --filters "Name=group-name,Values=$PROJECT_NAME-ecs-sg" "Name=vpc-id,Values=$VPC_ID" --query 'SecurityGroups[0].GroupId' --output text)
ALB_SG_ID=$(aws ec2 describe-security-groups --filters "Name=group-name,Values=$PROJECT_NAME-alb-sg" "Name=vpc-id,Values=$VPC_ID" --query 'SecurityGroups[0].GroupId' --output text)

# 1. Login to ECR
echo -e "${YELLOW}🔐 Logging into ECR...${NC}"
aws ecr get-login-password --region $AWS_REGION | docker login --username AWS --password-stdin $AWS_ACCOUNT_ID.dkr.ecr.$AWS_REGION.amazonaws.com

# 2. Build and push backend
echo -e "${YELLOW}🏗️  Building backend image...${NC}"
cd ../../backend
docker build -f Dockerfile.ecs -t $PROJECT_NAME-backend:latest .
docker tag $PROJECT_NAME-backend:latest $AWS_ACCOUNT_ID.dkr.ecr.$AWS_REGION.amazonaws.com/$PROJECT_NAME-backend:latest

echo -e "${YELLOW}📤 Pushing backend image to ECR...${NC}"
docker push $AWS_ACCOUNT_ID.dkr.ecr.$AWS_REGION.amazonaws.com/$PROJECT_NAME-backend:latest

# 3. Build and push frontend
echo -e "${YELLOW}🏗️  Building frontend image...${NC}"
cd ../frontend
docker build -f Dockerfile.ecs -t $PROJECT_NAME-frontend:latest .
docker tag $PROJECT_NAME-frontend:latest $AWS_ACCOUNT_ID.dkr.ecr.$AWS_REGION.amazonaws.com/$PROJECT_NAME-frontend:latest

echo -e "${YELLOW}📤 Pushing frontend image to ECR...${NC}"
docker push $AWS_ACCOUNT_ID.dkr.ecr.$AWS_REGION.amazonaws.com/$PROJECT_NAME-frontend:latest

# 4. Create Application Load Balancer
echo -e "${YELLOW}⚖️  Creating Application Load Balancer...${NC}"

ALB_NAME="$PROJECT_NAME-alb"
ALB_ARN=$(aws elbv2 describe-load-balancers --names $ALB_NAME --query 'LoadBalancers[0].LoadBalancerArn' --output text 2>/dev/null)

if [ "$ALB_ARN" = "None" ] || [ -z "$ALB_ARN" ]; then
    echo "Creating ALB..."
    ALB_ARN=$(aws elbv2 create-load-balancer \
        --name $ALB_NAME \
        --subnets $PUBLIC_SUBNET_1_ID $PUBLIC_SUBNET_2_ID \
        --security-groups $ALB_SG_ID \
        --scheme internet-facing \
        --type application \
        --ip-address-type ipv4 \
        --query 'LoadBalancers[0].LoadBalancerArn' \
        --output text)
    
    # Wait for ALB to be active
    echo "Waiting for ALB to be active..."
    aws elbv2 wait load-balancer-available --load-balancer-arns $ALB_ARN
else
    echo "ALB already exists: $ALB_NAME"
fi

# Get ALB DNS name
ALB_DNS=$(aws elbv2 describe-load-balancers --load-balancer-arns $ALB_ARN --query 'LoadBalancers[0].DNSName' --output text)

# 5. Create Target Groups
echo -e "${YELLOW}🎯 Creating target groups...${NC}"

# Backend target group
BACKEND_TG_ARN=$(aws elbv2 describe-target-groups --names "$PROJECT_NAME-backend-tg" --query 'TargetGroups[0].TargetGroupArn' --output text 2>/dev/null)
if [ "$BACKEND_TG_ARN" = "None" ] || [ -z "$BACKEND_TG_ARN" ]; then
    BACKEND_TG_ARN=$(aws elbv2 create-target-group \
        --name "$PROJECT_NAME-backend-tg" \
        --protocol HTTP \
        --port 3000 \
        --vpc-id $VPC_ID \
        --target-type ip \
        --health-check-path "/health" \
        --health-check-interval-seconds 30 \
        --health-check-timeout-seconds 5 \
        --healthy-threshold-count 2 \
        --unhealthy-threshold-count 3 \
        --query 'TargetGroups[0].TargetGroupArn' \
        --output text)
fi

# Frontend target group
FRONTEND_TG_ARN=$(aws elbv2 describe-target-groups --names "$PROJECT_NAME-frontend-tg" --query 'TargetGroups[0].TargetGroupArn' --output text 2>/dev/null)
if [ "$FRONTEND_TG_ARN" = "None" ] || [ -z "$FRONTEND_TG_ARN" ]; then
    FRONTEND_TG_ARN=$(aws elbv2 create-target-group \
        --name "$PROJECT_NAME-frontend-tg" \
        --protocol HTTP \
        --port 80 \
        --vpc-id $VPC_ID \
        --target-type ip \
        --health-check-path "/health" \
        --health-check-interval-seconds 30 \
        --health-check-timeout-seconds 5 \
        --healthy-threshold-count 2 \
        --unhealthy-threshold-count 3 \
        --query 'TargetGroups[0].TargetGroupArn' \
        --output text)
fi

# 6. Create ALB Listeners
echo -e "${YELLOW}👂 Creating ALB listeners...${NC}"

# HTTP Listener for frontend (default)
HTTP_LISTENER_ARN=$(aws elbv2 describe-listeners --load-balancer-arn $ALB_ARN --query 'Listeners[?Port==`80`].ListenerArn' --output text 2>/dev/null)
if [ -z "$HTTP_LISTENER_ARN" ] || [ "$HTTP_LISTENER_ARN" = "None" ]; then
    HTTP_LISTENER_ARN=$(aws elbv2 create-listener \
        --load-balancer-arn $ALB_ARN \
        --protocol HTTP \
        --port 80 \
        --default-actions Type=forward,TargetGroupArn=$FRONTEND_TG_ARN \
        --query 'Listeners[0].ListenerArn' \
        --output text)
fi

# Create rule for API traffic
aws elbv2 create-rule \
    --listener-arn $HTTP_LISTENER_ARN \
    --priority 100 \
    --conditions Field=path-pattern,Values="/api/*" \
    --actions Type=forward,TargetGroupArn=$BACKEND_TG_ARN 2>/dev/null || true

echo -e "${GREEN}✅ Load balancer configured${NC}"

# 7. Create and register task definitions
echo -e "${YELLOW}📋 Creating task definitions...${NC}"

cd ../scripts/aws

# Create backend task definition
cat > backend-task-definition.json << EOF
{
  "family": "$PROJECT_NAME-backend",
  "networkMode": "awsvpc",
  "requiresCompatibilities": ["FARGATE"],
  "cpu": "256",
  "memory": "512",
  "executionRoleArn": "arn:aws:iam::$AWS_ACCOUNT_ID:role/$PROJECT_NAME-ecs-execution-role",
  "taskRoleArn": "arn:aws:iam::$AWS_ACCOUNT_ID:role/$PROJECT_NAME-ecs-task-role",
  "containerDefinitions": [
    {
      "name": "$PROJECT_NAME-backend",
      "image": "$AWS_ACCOUNT_ID.dkr.ecr.$AWS_REGION.amazonaws.com/$PROJECT_NAME-backend:latest",
      "portMappings": [
        {
          "containerPort": 3000,
          "protocol": "tcp"
        }
      ],
      "essential": true,
      "environment": [
        {
          "name": "NODE_ENV",
          "value": "production"
        },
        {
          "name": "HOST",
          "value": "0.0.0.0"
        },
        {
          "name": "PORT",
          "value": "3000"
        },
        {
          "name": "DB_HOST",
          "value": "$AURORA_ENDPOINT"
        },
        {
          "name": "DB_PORT",
          "value": "5432"
        },
        {
          "name": "DB_USERNAME",
          "value": "postgres"
        },
        {
          "name": "DB_DATABASE",
          "value": "tuchanga"
        },
        {
          "name": "JWT_EXPIRES_IN",
          "value": "24h"
        },
        {
          "name": "FIREBASE_PROJECT_ID",
          "value": "tu-changa-583b3"
        },
        {
          "name": "CORS_ORIGIN",
          "value": "*"
        },
        {
          "name": "API_PREFIX",
          "value": ""
        }
      ],
      "secrets": [
        {
          "name": "DB_PASSWORD",
          "valueFrom": "arn:aws:secretsmanager:$AWS_REGION:$AWS_ACCOUNT_ID:secret:$PROJECT_NAME/db-password"
        },
        {
          "name": "JWT_SECRET",
          "valueFrom": "arn:aws:secretsmanager:$AWS_REGION:$AWS_ACCOUNT_ID:secret:$PROJECT_NAME/jwt-secret"
        },
        {
          "name": "DATABASE_URL",
          "valueFrom": "arn:aws:secretsmanager:$AWS_REGION:$AWS_ACCOUNT_ID:secret:$PROJECT_NAME/database-url"
        }
      ],
      "logConfiguration": {
        "logDriver": "awslogs",
        "options": {
          "awslogs-group": "/ecs/$PROJECT_NAME-backend",
          "awslogs-region": "$AWS_REGION",
          "awslogs-stream-prefix": "ecs"
        }
      },
      "healthCheck": {
        "command": [
          "CMD-SHELL",
          "curl -f http://localhost:3000/health || exit 1"
        ],
        "interval": 30,
        "timeout": 5,
        "retries": 3,
        "startPeriod": 60
      }
    }
  ]
}
EOF

# Create frontend task definition
cat > frontend-task-definition.json << EOF
{
  "family": "$PROJECT_NAME-frontend",
  "networkMode": "awsvpc",
  "requiresCompatibilities": ["FARGATE"],
  "cpu": "256",
  "memory": "512",
  "executionRoleArn": "arn:aws:iam::$AWS_ACCOUNT_ID:role/$PROJECT_NAME-ecs-execution-role",
  "containerDefinitions": [
    {
      "name": "$PROJECT_NAME-frontend",
      "image": "$AWS_ACCOUNT_ID.dkr.ecr.$AWS_REGION.amazonaws.com/$PROJECT_NAME-frontend:latest",
      "portMappings": [
        {
          "containerPort": 80,
          "protocol": "tcp"
        }
      ],
      "essential": true,
      "environment": [
        {
          "name": "BACKEND_URL",
          "value": "http://$ALB_DNS"
        }
      ],
      "logConfiguration": {
        "logDriver": "awslogs",
        "options": {
          "awslogs-group": "/ecs/$PROJECT_NAME-frontend",
          "awslogs-region": "$AWS_REGION",
          "awslogs-stream-prefix": "ecs"
        }
      },
      "healthCheck": {
        "command": [
          "CMD-SHELL",
          "curl -f http://localhost:80/health || exit 1"
        ],
        "interval": 30,
        "timeout": 5,
        "retries": 3,
        "startPeriod": 30
      }
    }
  ]
}
EOF

# Register task definitions
echo "Registering backend task definition..."
BACKEND_TASK_DEF_ARN=$(aws ecs register-task-definition \
    --cli-input-json file://backend-task-definition.json \
    --query 'taskDefinition.taskDefinitionArn' \
    --output text)

echo "Registering frontend task definition..."
FRONTEND_TASK_DEF_ARN=$(aws ecs register-task-definition \
    --cli-input-json file://frontend-task-definition.json \
    --query 'taskDefinition.taskDefinitionArn' \
    --output text)

# 8. Create ECS Services
echo -e "${YELLOW}🚀 Creating ECS services...${NC}"

CLUSTER_NAME="$PROJECT_NAME-cluster"

# Backend service
BACKEND_SERVICE_NAME="$PROJECT_NAME-backend-service"
if ! service_exists "$CLUSTER_NAME" "$BACKEND_SERVICE_NAME" >/dev/null 2>&1; then
    echo "Creating backend service..."
    aws ecs create-service \
        --cluster $CLUSTER_NAME \
        --service-name $BACKEND_SERVICE_NAME \
        --task-definition $BACKEND_TASK_DEF_ARN \
        --desired-count 1 \
        --launch-type FARGATE \
        --network-configuration "awsvpcConfiguration={subnets=[$PRIVATE_SUBNET_1_ID,$PRIVATE_SUBNET_2_ID],securityGroups=[$ECS_SG_ID],assignPublicIp=DISABLED}" \
        --load-balancers targetGroupArn=$BACKEND_TG_ARN,containerName=$PROJECT_NAME-backend,containerPort=3000
else
    echo "Updating backend service..."
    aws ecs update-service \
        --cluster $CLUSTER_NAME \
        --service $BACKEND_SERVICE_NAME \
        --task-definition $BACKEND_TASK_DEF_ARN \
        --force-new-deployment
fi

# Frontend service
FRONTEND_SERVICE_NAME="$PROJECT_NAME-frontend-service"
if ! service_exists "$CLUSTER_NAME" "$FRONTEND_SERVICE_NAME" >/dev/null 2>&1; then
    echo "Creating frontend service..."
    aws ecs create-service \
        --cluster $CLUSTER_NAME \
        --service-name $FRONTEND_SERVICE_NAME \
        --task-definition $FRONTEND_TASK_DEF_ARN \
        --desired-count 1 \
        --launch-type FARGATE \
        --network-configuration "awsvpcConfiguration={subnets=[$PRIVATE_SUBNET_1_ID,$PRIVATE_SUBNET_2_ID],securityGroups=[$ECS_SG_ID],assignPublicIp=DISABLED}" \
        --load-balancers targetGroupArn=$FRONTEND_TG_ARN,containerName=$PROJECT_NAME-frontend,containerPort=80
else
    echo "Updating frontend service..."
    aws ecs update-service \
        --cluster $CLUSTER_NAME \
        --service $FRONTEND_SERVICE_NAME \
        --task-definition $FRONTEND_TASK_DEF_ARN \
        --force-new-deployment
fi

# 9. Wait for services to stabilize
echo -e "${YELLOW}⏳ Waiting for services to stabilize...${NC}"
aws ecs wait services-stable --cluster $CLUSTER_NAME --services $BACKEND_SERVICE_NAME $FRONTEND_SERVICE_NAME

# Clean up temporary files
rm -f backend-task-definition.json frontend-task-definition.json

echo -e "${GREEN}🎉 Container deployment completed successfully!${NC}"
echo -e "${BLUE}📋 Deployment Summary:${NC}"
echo "Backend Service: $BACKEND_SERVICE_NAME"
echo "Frontend Service: $FRONTEND_SERVICE_NAME"
echo "Load Balancer: $ALB_DNS"
echo ""
echo -e "${GREEN}🌐 Your application is available at: http://$ALB_DNS${NC}"
