# 🚀 Tuchanga - Despliegue Nativo en EC2

## 📋 Resumen de Cambios

Los scripts han sido completamente modificados para realizar un **despliegue nativo** sin Docker, utilizando tecnologías nativas del sistema operativo para mejor rendimiento y simplicidad.

## 🔄 Cambios Principales

### ❌ **Eliminado (Docker)**
- Docker Engine
- Docker Compose
- Contenedores Docker
- Imágenes Docker
- Redes Docker
- Volúmenes Docker
- Scripts de permisos Docker

### ✅ **Ag<PERSON>gado (Nativo)**
- **PostgreSQL nativo** del sistema
- **PM2** para gestión de procesos Node.js
- **Aplicación compilada** ejecutándose directamente
- **Configuración nativa** de servicios

## 🏗️ **Nueva Arquitectura**

```
┌─────────────────────────────────────────┐
│                 EC2 Ubuntu 24 LTS       │
├─────────────────────────────────────────┤
│  🌐 Nginx (Proxy + SSL + Static Files)  │
├─────────────────────────────────────────┤
│  ⚙️  PM2 → Node.js <PERSON> (Puerto 3000) │
├─────────────────────────────────────────┤
│  🗄️  PostgreSQL Nativo (Puerto 5432)    │
├─────────────────────────────────────────┤
│  📁 Sistema de Archivos Ubuntu          │
└─────────────────────────────────────────┘
```

## 📦 **Tecnologías Utilizadas**

| Componente | Antes (Docker) | Ahora (Nativo) |
|------------|----------------|----------------|
| **Base de datos** | PostgreSQL en contenedor | PostgreSQL nativo del sistema |
| **Backend** | Node.js en contenedor | Node.js con PM2 |
| **Frontend** | Nginx sirviendo archivos | Nginx sirviendo archivos |
| **Gestión de procesos** | Docker Compose | PM2 + systemd |
| **Logs** | Docker logs | PM2 logs + archivos del sistema |
| **Monitoreo** | Docker stats | PM2 monit + systemctl |
| **Backups** | pg_dump en contenedor | pg_dump nativo |

## 🚀 **Beneficios del Despliegue Nativo**

### ⚡ **Rendimiento**
- **Sin overhead de contenedores**: Ejecución directa en el sistema
- **Mejor uso de memoria**: Sin capas adicionales de virtualización
- **I/O más rápido**: Acceso directo al sistema de archivos
- **Red nativa**: Sin bridges ni proxies internos

### 🔧 **Simplicidad**
- **Sin problemas de permisos Docker**: Eliminados completamente
- **Configuración más directa**: Variables de entorno estándar
- **Debugging más fácil**: Logs y procesos nativos del sistema
- **Menos dependencias**: Solo las necesarias para la aplicación

### 🛠️ **Gestión**
- **PM2 profesional**: Gestión avanzada de procesos Node.js
- **systemctl estándar**: Gestión de servicios con herramientas del sistema
- **Logs centralizados**: journalctl + PM2 logs
- **Monitoreo integrado**: Herramientas nativas del sistema

### 🔒 **Seguridad**
- **Menos superficie de ataque**: Sin daemon Docker
- **Permisos estándar**: Usuarios y grupos del sistema
- **Firewall simplificado**: Solo puertos necesarios
- **Actualizaciones del sistema**: Gestión estándar con apt

## 📁 **Estructura de Archivos**

```
/opt/tuchanga/                    # Proyecto principal
├── backend/                      # Código del backend
│   ├── dist/                     # Código compilado
│   ├── node_modules/             # Dependencias
│   └── package.json
├── frontend/                     # Código del frontend
│   ├── dist/                     # Build de producción
│   └── package.json
├── ecosystem.config.js           # Configuración PM2
├── .env                          # Variables de entorno
└── scripts/allInEc2/             # Scripts de despliegue

/var/www/tuchanga/                # Archivos estáticos del frontend
├── index.html
├── assets/
└── ...

/var/log/tuchanga/                # Logs de la aplicación
├── backend-error.log
├── backend-out.log
└── backend-combined.log
```

## 🔧 **Comandos de Gestión**

### **PM2 (Backend)**
```bash
# Estado de procesos
pm2 status

# Logs en tiempo real
pm2 logs tuchanga-backend

# Reiniciar backend
pm2 restart tuchanga-backend

# Monitoreo de recursos
pm2 monit

# Información detallada
pm2 show tuchanga-backend
```

### **PostgreSQL (Base de datos)**
```bash
# Estado del servicio
sudo systemctl status postgresql

# Reiniciar servicio
sudo systemctl restart postgresql

# Conectar a la base de datos
psql -h localhost -U tuchanga_user -d tuchanga

# Logs del servicio
sudo journalctl -u postgresql
```

### **Nginx (Web server)**
```bash
# Estado del servicio
sudo systemctl status nginx

# Reiniciar servicio
sudo systemctl restart nginx

# Verificar configuración
sudo nginx -t

# Logs de acceso y errores
sudo tail -f /var/log/nginx/access.log
sudo tail -f /var/log/nginx/error.log
```

## 🔄 **Proceso de Despliegue**

1. **Instalación de dependencias**: Node.js, PostgreSQL, PM2, Nginx
2. **Configuración de PostgreSQL**: Usuario, base de datos, permisos
3. **Construcción de la aplicación**: Backend (TypeScript) + Frontend (React)
4. **Configuración de PM2**: Ecosystem file, logs, monitoreo
5. **Despliegue del frontend**: Archivos estáticos en Nginx
6. **Configuración de SSL**: Let's Encrypt o certificados auto-firmados
7. **Configuración de Nginx**: Proxy reverso + archivos estáticos
8. **Inicio de servicios**: PM2 + PostgreSQL + Nginx

## 📊 **Monitoreo y Logs**

### **Logs Disponibles**
- **PM2 Backend**: `/var/log/tuchanga/backend-*.log`
- **PostgreSQL**: `journalctl -u postgresql`
- **Nginx**: `/var/log/nginx/access.log` y `/var/log/nginx/error.log`
- **Sistema**: `journalctl`

### **Comandos de Monitoreo**
```bash
# Estado completo del sistema
./check-status.sh

# Logs específicos
./show-logs.sh backend
./show-logs.sh database
./show-logs.sh nginx

# Monitoreo en tiempo real
pm2 monit
sudo journalctl -f
```

## 🎯 **Migración desde Docker**

Si tenías una instalación previa con Docker:

1. **Backup de datos**:
   ```bash
   docker exec tuchanga-postgres pg_dump -U postgres tuchanga > backup.sql
   ```

2. **Detener contenedores**:
   ```bash
   docker-compose down
   ```

3. **Ejecutar nueva instalación**:
   ```bash
   ./main.sh  # Opción 1: Instalación completa
   ```

4. **Restaurar datos** (si es necesario):
   ```bash
   psql -h localhost -U tuchanga_user -d tuchanga < backup.sql
   ```

## ✅ **Verificación de la Instalación**

Después de la instalación, verifica que todo funcione:

```bash
# 1. Estado de servicios
./check-status.sh

# 2. Verificar PostgreSQL
sudo systemctl status postgresql
psql -h localhost -U tuchanga_user -d tuchanga -c "SELECT 1;"

# 3. Verificar PM2
pm2 status
pm2 logs tuchanga-backend --lines 10

# 4. Verificar Nginx
sudo systemctl status nginx
curl -I http://localhost

# 5. Verificar aplicación
curl http://localhost:3000/health
curl https://tu-dominio.com
```

## 🎉 **Resultado Final**

Una vez completada la instalación tendrás:

- ✅ **PostgreSQL nativo** ejecutándose como servicio del sistema
- ✅ **Backend Node.js** gestionado por PM2 con auto-restart
- ✅ **Frontend React** servido por Nginx con SSL
- ✅ **Certificados SSL** automáticos con Let's Encrypt
- ✅ **Monitoreo integrado** con PM2 y systemctl
- ✅ **Logs centralizados** y fáciles de acceder
- ✅ **Backups automáticos** de la base de datos
- ✅ **Gestión simplificada** sin Docker

**¡Tu aplicación Tuchanga estará ejecutándose de forma nativa, eficiente y profesional!** 🚀
