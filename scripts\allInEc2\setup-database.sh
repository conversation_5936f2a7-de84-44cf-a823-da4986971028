#!/bin/bash

# Setup PostgreSQL database for Tuchanga
# This script configures the native PostgreSQL database

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to check if command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Configuration
DB_PASSWORD="${1:-TuChanga2024SecureDB}"
DB_NAME="tuchanga"
DB_USER="tuchanga_user"
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_DIR="$(cd "$SCRIPT_DIR/../.." && pwd)"

print_status "Directorio del proyecto detectado: $PROJECT_DIR"

# Check prerequisites
check_prerequisites() {
    if ! command_exists psql; then
        print_error "PostgreSQL no está instalado. Ejecuta install-dependencies.sh primero"
        exit 1
    fi

    if ! systemctl is-active --quiet postgresql; then
        print_error "PostgreSQL no está ejecutándose"
        print_status "Iniciando PostgreSQL..."
        sudo systemctl start postgresql
        sudo systemctl enable postgresql

        if ! systemctl is-active --quiet postgresql; then
            print_error "No se pudo iniciar PostgreSQL"
            exit 1
        fi
    fi

    if [[ ! -d "$PROJECT_DIR" ]]; then
        print_error "Directorio del proyecto no encontrado: $PROJECT_DIR"
        print_error "Ejecuta setup-repository.sh primero"
        exit 1
    fi
}

# Check if database exists
database_exists() {
    sudo -u postgres psql -lqt | cut -d \| -f 1 | grep -qw "$DB_NAME" 2>/dev/null
}

# Check if user exists
user_exists() {
    sudo -u postgres psql -tAc "SELECT 1 FROM pg_roles WHERE rolname='$DB_USER'" | grep -q 1
}

# Update environment file with database password
update_environment() {
    print_status "Actualizando archivo de entorno con configuración de base de datos..."

    cd "$PROJECT_DIR"

    if [[ ! -f ".env" ]]; then
        print_error "Archivo .env no encontrado. Ejecuta setup-repository.sh primero"
        exit 1
    fi

    # Update database configuration
    sed -i "s/DB_HOST=.*/DB_HOST=localhost/" .env
    sed -i "s/DB_PORT=.*/DB_PORT=5432/" .env
    sed -i "s/DB_USERNAME=.*/DB_USERNAME=$DB_USER/" .env
    sed -i "s/DB_DATABASE=.*/DB_DATABASE=$DB_NAME/" .env

    if grep -q "DB_PASSWORD=" .env; then
        sed -i "s/DB_PASSWORD=.*/DB_PASSWORD=$DB_PASSWORD/" .env
    else
        echo "DB_PASSWORD=$DB_PASSWORD" >> .env
    fi

    # Update DATABASE_URL
    local database_url="postgresql://$DB_USER:$DB_PASSWORD@localhost:5432/$DB_NAME"
    if grep -q "DATABASE_URL=" .env; then
        sed -i "s|DATABASE_URL=.*|DATABASE_URL=$database_url|" .env
    else
        echo "DATABASE_URL=$database_url" >> .env
    fi

    print_success "Archivo de entorno actualizado"
}

# Create database user
create_user() {
    print_status "Configurando usuario de base de datos..."

    if user_exists; then
        print_success "El usuario '$DB_USER' ya existe"
    else
        print_status "Creando usuario '$DB_USER'..."
        sudo -u postgres psql -c "CREATE USER $DB_USER WITH PASSWORD '$DB_PASSWORD';"
        sudo -u postgres psql -c "ALTER USER $DB_USER CREATEDB;"
        print_success "Usuario '$DB_USER' creado exitosamente"
    fi
}

# Create database if it doesn't exist
create_database() {
    print_status "Verificando si la base de datos existe..."

    if database_exists; then
        print_success "La base de datos '$DB_NAME' ya existe"
    else
        print_status "Creando base de datos '$DB_NAME'..."
        sudo -u postgres createdb -O "$DB_USER" "$DB_NAME"
        print_success "Base de datos '$DB_NAME' creada exitosamente"
    fi

    # Grant permissions
    print_status "Configurando permisos de base de datos..."
    sudo -u postgres psql -c "GRANT ALL PRIVILEGES ON DATABASE $DB_NAME TO $DB_USER;"
    print_success "Permisos configurados correctamente"
}

# Configure PostgreSQL for remote connections
configure_postgresql() {
    print_status "Configurando PostgreSQL para conexiones locales..."

    local pg_version=$(sudo -u postgres psql -tAc "SELECT version();" | cut -d' ' -f2 | cut -d'.' -f1)
    local pg_config_dir="/etc/postgresql/$pg_version/main"

    # Update postgresql.conf
    if [[ -f "$pg_config_dir/postgresql.conf" ]]; then
        print_status "Configurando postgresql.conf..."
        sudo sed -i "s/#listen_addresses = 'localhost'/listen_addresses = 'localhost'/" "$pg_config_dir/postgresql.conf"
        sudo sed -i "s/#port = 5432/port = 5432/" "$pg_config_dir/postgresql.conf"
    fi

    # Update pg_hba.conf for local connections
    if [[ -f "$pg_config_dir/pg_hba.conf" ]]; then
        print_status "Configurando pg_hba.conf..."
        # Add rule for local connections with password
        if ! sudo grep -q "local.*$DB_NAME.*$DB_USER.*md5" "$pg_config_dir/pg_hba.conf"; then
            echo "local   $DB_NAME   $DB_USER   md5" | sudo tee -a "$pg_config_dir/pg_hba.conf" >/dev/null
        fi
    fi

    # Restart PostgreSQL to apply changes
    print_status "Reiniciando PostgreSQL para aplicar cambios..."
    sudo systemctl restart postgresql

    # Wait for PostgreSQL to be ready
    sleep 3

    if systemctl is-active --quiet postgresql; then
        print_success "PostgreSQL configurado y reiniciado correctamente"
    else
        print_error "Error al reiniciar PostgreSQL"
        exit 1
    fi
}

# Create database backup
create_backup() {
    if ! systemctl is-active --quiet postgresql; then
        print_warning "PostgreSQL no está ejecutándose, saltando backup"
        return 0
    fi

    if ! database_exists; then
        print_warning "Base de datos no existe, saltando backup"
        return 0
    fi

    print_status "Creando backup de la base de datos..."

    local backup_dir="/opt/tuchanga-backups"
    local backup_file="$backup_dir/tuchanga-backup-$(date +%Y%m%d-%H%M%S).sql"

    # Create backup directory
    sudo mkdir -p "$backup_dir"
    sudo chown $USER:$USER "$backup_dir"

    # Create backup
    sudo -u postgres pg_dump "$DB_NAME" > "$backup_file"

    if [[ -f "$backup_file" ]] && [[ -s "$backup_file" ]]; then
        print_success "Backup creado: $backup_file"

        # Keep only last 5 backups
        cd "$backup_dir"
        ls -t tuchanga-backup-*.sql | tail -n +6 | xargs -r rm
        print_status "Manteniendo solo los últimos 5 backups"
    else
        print_warning "Error al crear backup"
    fi
}

# Show database status
show_database_status() {
    print_status "Estado de la base de datos:"
    echo ""

    if systemctl is-active --quiet postgresql; then
        echo "✅ PostgreSQL: Ejecutándose"
        echo "🗄️  Base de datos: $DB_NAME"
        echo "👤 Usuario: $DB_USER"
        echo "🔌 Puerto: 5432"
        echo "🏠 Host: localhost"

        # Show database size
        local db_size=$(sudo -u postgres psql -d "$DB_NAME" -t -c "SELECT pg_size_pretty(pg_database_size('$DB_NAME'));" 2>/dev/null | xargs || echo "N/A")
        echo "📏 Tamaño: $db_size"

        # Show tables count
        local tables_count=$(sudo -u postgres psql -d "$DB_NAME" -t -c "SELECT count(*) FROM information_schema.tables WHERE table_schema = 'public';" 2>/dev/null | xargs || echo "N/A")
        echo "📋 Tablas: $tables_count"

        # Show connections
        local connections=$(sudo -u postgres psql -d "$DB_NAME" -t -c "SELECT count(*) FROM pg_stat_activity WHERE datname = '$DB_NAME';" 2>/dev/null | xargs || echo "N/A")
        echo "🔗 Conexiones activas: $connections"

    else
        echo "❌ PostgreSQL: No ejecutándose"
    fi

    echo ""
}

# Main function
main() {
    print_status "Configurando base de datos PostgreSQL nativa para Tuchanga..."
    echo ""

    check_prerequisites

    # Create backup if database exists
    create_backup

    update_environment
    create_user
    create_database
    configure_postgresql

    echo ""
    show_database_status

    print_success "¡Base de datos configurada exitosamente!"
    echo ""
    print_status "Información de conexión:"
    echo "- Host: localhost"
    echo "- Puerto: 5432"
    echo "- Base de datos: $DB_NAME"
    echo "- Usuario: $DB_USER"
    echo "- Contraseña: $DB_PASSWORD"
    echo ""
    print_status "Para conectarte manualmente:"
    echo "psql -h localhost -p 5432 -U $DB_USER -d $DB_NAME"
    echo ""
    print_status "Próximos pasos:"
    echo "1. Configurar aplicación: ./start-application.sh"
    echo "2. Ver estado: ./check-status.sh"
    echo ""
}

# Run main function
main "$@"
