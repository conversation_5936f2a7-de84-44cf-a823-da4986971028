#!/bin/bash

# Restart Tuchanga services
# This script restarts all services in the correct order

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_DIR="$(cd "$SCRIPT_DIR/../.." && pwd)"

# Function to check if command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Check prerequisites
check_prerequisites() {
    if ! command_exists pm2; then
        print_error "PM2 no está instalado"
        exit 1
    fi

    if [[ ! -d "$PROJECT_DIR" ]]; then
        print_error "Directorio del proyecto no encontrado: $PROJECT_DIR"
        exit 1
    fi
}

# Restart Nginx
restart_nginx() {
    print_status "Reiniciando Nginx..."
    
    if systemctl is-active --quiet nginx; then
        sudo systemctl restart nginx
        
        # Wait for Nginx to start
        sleep 2
        
        if systemctl is-active --quiet nginx; then
            print_success "Nginx reiniciado exitosamente"
        else
            print_error "Error al reiniciar Nginx"
            return 1
        fi
    else
        print_status "Nginx no estaba ejecutándose, iniciando..."
        sudo systemctl start nginx
        
        if systemctl is-active --quiet nginx; then
            print_success "Nginx iniciado exitosamente"
        else
            print_error "Error al iniciar Nginx"
            return 1
        fi
    fi
}

# Restart database
restart_database() {
    print_status "Reiniciando PostgreSQL..."

    if systemctl is-active --quiet postgresql; then
        sudo systemctl restart postgresql

        # Wait for database to be ready
        print_status "Esperando que PostgreSQL esté listo..."
        local max_attempts=30
        local attempt=1

        while [[ $attempt -le $max_attempts ]]; do
            if sudo -u postgres psql -d tuchanga -c "SELECT 1;" >/dev/null 2>&1; then
                print_success "PostgreSQL reiniciado y listo"
                return 0
            fi

            print_status "Intento $attempt/$max_attempts - Esperando PostgreSQL..."
            sleep 2
            ((attempt++))
        done

        print_error "PostgreSQL no respondió después de $max_attempts intentos"
        return 1
    else
        print_status "PostgreSQL no estaba ejecutándose, iniciando..."
        sudo systemctl start postgresql

        # Wait for database to be ready
        print_status "Esperando que PostgreSQL esté listo..."
        local max_attempts=30
        local attempt=1

        while [[ $attempt -le $max_attempts ]]; do
            if sudo -u postgres psql -d tuchanga -c "SELECT 1;" >/dev/null 2>&1; then
                print_success "PostgreSQL iniciado y listo"
                return 0
            fi

            print_status "Intento $attempt/$max_attempts - Esperando PostgreSQL..."
            sleep 2
            ((attempt++))
        done

        print_error "PostgreSQL no respondió después de $max_attempts intentos"
        return 1
    fi
}

# Restart backend
restart_backend() {
    print_status "Reiniciando backend con PM2..."

    if pm2 list | grep -q "tuchanga-backend"; then
        pm2 restart tuchanga-backend
    else
        print_status "Backend no estaba ejecutándose, iniciando..."
        cd "$PROJECT_DIR"
        pm2 start ecosystem.config.js
    fi

    # Wait for backend to be ready
    print_status "Esperando que el backend esté listo..."
    local max_attempts=60
    local attempt=1

    while [[ $attempt -le $max_attempts ]]; do
        if curl -f http://localhost:3000/health >/dev/null 2>&1; then
            print_success "Backend reiniciado y listo"
            return 0
        fi

        print_status "Intento $attempt/$max_attempts - Esperando backend..."
        sleep 3
        ((attempt++))
    done

    print_error "El backend no respondió después de $max_attempts intentos"
    print_status "Mostrando logs del backend:"
    pm2 logs tuchanga-backend --lines 10
    return 1
}

# Restart all services
restart_all_services() {
    print_status "Reiniciando todos los servicios..."

    # Restart PostgreSQL
    restart_database

    # Restart backend
    restart_backend

    # Restart Nginx
    restart_nginx

    print_success "Todos los servicios reiniciados"
}

# Show restart menu
show_restart_menu() {
    echo ""
    print_status "Selecciona qué reiniciar:"
    echo "1. 🔄 Reiniciar todo (recomendado)"
    echo "2. 🌐 Reiniciar solo Nginx"
    echo "3. 🗄️  Reiniciar solo PostgreSQL"
    echo "4. ⚙️  Reiniciar solo backend (PM2)"
    echo "0. ❌ Cancelar"
    echo ""
}

# Show service status after restart
show_status() {
    print_status "Estado de los servicios después del reinicio:"
    echo ""
    
    # Nginx status
    if systemctl is-active --quiet nginx; then
        echo "✅ Nginx: Ejecutándose"
    else
        echo "❌ Nginx: No ejecutándose"
    fi
    
    # Database status
    if docker ps --format "{{.Names}}" | grep -q "tuchanga-postgres"; then
        if docker exec tuchanga-postgres pg_isready -U postgres -d tuchanga >/dev/null 2>&1; then
            echo "✅ Base de datos: Ejecutándose y lista"
        else
            echo "⚠️  Base de datos: Ejecutándose pero no responde"
        fi
    else
        echo "❌ Base de datos: No ejecutándose"
    fi
    
    # Backend status
    if docker ps --format "{{.Names}}" | grep -q "tuchanga-backend"; then
        if curl -f http://localhost:3000/health >/dev/null 2>&1; then
            echo "✅ Backend: Ejecutándose y saludable"
        else
            echo "⚠️  Backend: Ejecutándose pero no responde"
        fi
    else
        echo "❌ Backend: No ejecutándose"
    fi
    
    echo ""
    
    # Show PM2 status
    print_status "Estado de procesos PM2:"
    pm2 list | grep tuchanga || echo "No hay procesos de Tuchanga ejecutándose"
    
    echo ""
}

# Main function
main() {
    print_status "Reiniciando servicios de Tuchanga..."
    echo ""
    
    check_prerequisites
    
    # If no arguments provided, show menu
    if [[ $# -eq 0 ]]; then
        show_restart_menu
        read -p "Ingresa tu opción [0-5]: " choice
        
        case $choice in
            1)
                print_status "Reiniciando todos los servicios..."
                restart_all_services
                ;;
            2)
                restart_nginx
                ;;
            3)
                restart_database
                ;;
            4)
                restart_backend
                ;;
            0)
                print_status "Operación cancelada"
                exit 0
                ;;
            *)
                print_error "Opción inválida"
                exit 1
                ;;
        esac
    else
        # Handle command line arguments
        case "$1" in
            "all"|"todo")
                restart_all_services
                ;;
            "nginx")
                restart_nginx
                ;;
            "database"|"db"|"postgresql")
                restart_database
                ;;
            "backend"|"api"|"pm2")
                restart_backend
                ;;
            *)
                print_error "Argumento inválido: $1"
                echo "Uso: $0 [all|nginx|database|backend]"
                exit 1
                ;;
        esac
    fi
    
    # Wait a moment for services to stabilize
    sleep 5
    
    show_status
    
    print_success "¡Reinicio completado!"
    echo ""
    print_status "Comandos útiles:"
    echo "- Ver estado completo: ./check-status.sh"
    echo "- Ver logs: ./show-logs.sh"
    echo "- Detener servicios: ./stop-services.sh"
}

# Run main function
main "$@"
