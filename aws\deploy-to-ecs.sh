#!/bin/bash

# Automated deployment script for TuChanga to AWS ECS
# Prerequisites: AWS CLI configured, Docker installed, ECR repositories created

set -e

# Configuration
AWS_REGION="sa-east-1"
AWS_ACCOUNT_ID=$(aws sts get-caller-identity --query Account --output text)
ECR_BACKEND_REPO="tuchanga-backend"
ECR_FRONTEND_REPO="tuchanga-frontend"
ECS_CLUSTER="tuchanga-cluster"
BACKEND_SERVICE="tuchanga-backend-service"
FRONTEND_SERVICE="tuchanga-frontend-service"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

echo -e "${GREEN}🚀 Starting TuChanga deployment to AWS ECS${NC}"

# Function to check if command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Check prerequisites
echo -e "${YELLOW}📋 Checking prerequisites...${NC}"
if ! command_exists aws; then
    echo -e "${RED}❌ AWS CLI not found. Please install it first.${NC}"
    exit 1
fi

if ! command_exists docker; then
    echo -e "${RED}❌ Docker not found. Please install it first.${NC}"
    exit 1
fi

# Login to ECR
echo -e "${YELLOW}🔐 Logging into ECR...${NC}"
aws ecr get-login-password --region $AWS_REGION | docker login --username AWS --password-stdin $AWS_ACCOUNT_ID.dkr.ecr.$AWS_REGION.amazonaws.com

# Build and push backend
echo -e "${YELLOW}🏗️  Building backend image...${NC}"
cd ../backend
docker build -f Dockerfile.ecs -t $ECR_BACKEND_REPO:latest .
docker tag $ECR_BACKEND_REPO:latest $AWS_ACCOUNT_ID.dkr.ecr.$AWS_REGION.amazonaws.com/$ECR_BACKEND_REPO:latest

echo -e "${YELLOW}📤 Pushing backend image to ECR...${NC}"
docker push $AWS_ACCOUNT_ID.dkr.ecr.$AWS_REGION.amazonaws.com/$ECR_BACKEND_REPO:latest

# Build and push frontend
echo -e "${YELLOW}🏗️  Building frontend image...${NC}"
cd ../frontend
docker build -f Dockerfile.ecs -t $ECR_FRONTEND_REPO:latest .
docker tag $ECR_FRONTEND_REPO:latest $AWS_ACCOUNT_ID.dkr.ecr.$AWS_REGION.amazonaws.com/$ECR_FRONTEND_REPO:latest

echo -e "${YELLOW}📤 Pushing frontend image to ECR...${NC}"
docker push $AWS_ACCOUNT_ID.dkr.ecr.$AWS_REGION.amazonaws.com/$ECR_FRONTEND_REPO:latest

# Update task definitions with current account ID
echo -e "${YELLOW}📝 Updating task definitions...${NC}"
cd ../aws

# Update backend task definition
sed "s/ACCOUNT_ID/$AWS_ACCOUNT_ID/g" ecs-task-definition-backend.json > ecs-task-definition-backend-updated.json

# Update frontend task definition
sed "s/ACCOUNT_ID/$AWS_ACCOUNT_ID/g" ecs-task-definition-frontend.json > ecs-task-definition-frontend-updated.json

# Register new task definitions
echo -e "${YELLOW}📋 Registering backend task definition...${NC}"
BACKEND_TASK_DEF_ARN=$(aws ecs register-task-definition \
    --cli-input-json file://ecs-task-definition-backend-updated.json \
    --query 'taskDefinition.taskDefinitionArn' \
    --output text)

echo -e "${YELLOW}📋 Registering frontend task definition...${NC}"
FRONTEND_TASK_DEF_ARN=$(aws ecs register-task-definition \
    --cli-input-json file://ecs-task-definition-frontend-updated.json \
    --query 'taskDefinition.taskDefinitionArn' \
    --output text)

# Update services
echo -e "${YELLOW}🔄 Updating backend service...${NC}"
aws ecs update-service \
    --cluster $ECS_CLUSTER \
    --service $BACKEND_SERVICE \
    --task-definition $BACKEND_TASK_DEF_ARN \
    --force-new-deployment

echo -e "${YELLOW}🔄 Updating frontend service...${NC}"
aws ecs update-service \
    --cluster $ECS_CLUSTER \
    --service $FRONTEND_SERVICE \
    --task-definition $FRONTEND_TASK_DEF_ARN \
    --force-new-deployment

# Wait for deployment to complete
echo -e "${YELLOW}⏳ Waiting for services to stabilize...${NC}"
aws ecs wait services-stable --cluster $ECS_CLUSTER --services $BACKEND_SERVICE $FRONTEND_SERVICE

# Clean up temporary files
rm -f ecs-task-definition-backend-updated.json ecs-task-definition-frontend-updated.json

echo -e "${GREEN}✅ Deployment completed successfully!${NC}"

# Get ALB DNS name
ALB_DNS=$(aws elbv2 describe-load-balancers \
    --query "LoadBalancers[?contains(LoadBalancerName, 'tuchanga')].DNSName" \
    --output text)

if [ ! -z "$ALB_DNS" ]; then
    echo -e "${GREEN}🌐 Your application is available at: https://$ALB_DNS${NC}"
else
    echo -e "${YELLOW}🌐 Check your ALB DNS name in the AWS Console${NC}"
fi
