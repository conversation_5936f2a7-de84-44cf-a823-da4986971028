#!/bin/bash

# Graceful Container Shutdown for Tuchanga
# This script stops containers one by one to avoid overwhelming the EC2 instance

set -e

echo "🛑 Tuchanga Graceful Container Shutdown"
echo "======================================="
echo ""

# Function to show resource usage
show_resources() {
    echo "📊 Current resource usage:"
    echo "Memory:"
    free -h | head -2
    echo "CPU Load:"
    uptime
    echo ""
}

# Function to wait for container to stop
wait_for_stop() {
    local container_name=$1
    local max_attempts=30
    local attempt=1
    
    echo "⏳ Waiting for $container_name to stop..."
    
    while [ $attempt -le $max_attempts ]; do
        if ! docker ps --filter "name=$container_name" --format "{{.Names}}" | grep -q "$container_name"; then
            echo "✅ $container_name stopped successfully!"
            return 0
        fi
        
        echo "   Attempt $attempt/$max_attempts - waiting..."
        sleep 2
        ((attempt++))
    done
    
    echo "⚠️  $container_name taking too long to stop, forcing..."
    docker kill $container_name 2>/dev/null || true
    return 1
}

echo "🔍 Current container status:"
docker ps --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}" --filter "name=job-platform" || echo "No containers running"

show_resources

echo ""
echo "🌐 Step 1: Stopping Frontend..."
echo "==============================="
if docker ps --filter "name=job-platform-frontend" --format "{{.Names}}" | grep -q "job-platform-frontend"; then
    docker stop job-platform-frontend
    wait_for_stop "job-platform-frontend"
else
    echo "Frontend container not running"
fi

show_resources

echo ""
echo "🔧 Step 2: Stopping Backend API..."
echo "=================================="
if docker ps --filter "name=job-platform-backend" --format "{{.Names}}" | grep -q "job-platform-backend"; then
    docker stop job-platform-backend
    wait_for_stop "job-platform-backend"
else
    echo "Backend container not running"
fi

show_resources

echo ""
echo "🗄️ Step 3: Stopping PostgreSQL Database..."
echo "==========================================="
if docker ps --filter "name=job-platform-postgres" --format "{{.Names}}" | grep -q "job-platform-postgres"; then
    docker stop job-platform-postgres
    wait_for_stop "job-platform-postgres"
else
    echo "Database container not running"
fi

show_resources

echo ""
echo "🧹 Step 4: Cleanup (optional)..."
echo "================================"
read -p "Do you want to remove stopped containers? (y/N): " cleanup
if [[ $cleanup =~ ^[Yy]$ ]]; then
    echo "Removing stopped containers..."
    docker container prune -f
    echo "✅ Cleanup complete"
else
    echo "Skipping cleanup"
fi

echo ""
echo "🔍 Final status:"
echo "================"
echo "Remaining containers:"
docker ps --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}" || echo "No containers running"

echo ""
echo "Port status:"
for port in 80 443 3000 5432; do
    if netstat -tuln | grep -q ":$port "; then
        echo "⚠️  Port $port: Still listening"
    else
        echo "✅ Port $port: Free"
    fi
done

show_resources

echo ""
echo "✅ Graceful shutdown complete!"
echo ""
echo "📋 To start again:"
echo "   ./start-containers-step-by-step.sh"
echo ""
echo "🔧 To start with automatic fixes:"
echo "   ./fix-deployment.sh"
