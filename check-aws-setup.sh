#!/bin/bash

# AWS EC2 Setup Checker for Tuchanga Job Platform
# Verifies security groups, instance metadata, and network configuration

set -e

echo "🔍 AWS EC2 Setup Checker for Tuchanga"
echo "====================================="

# Check if running on EC2
echo "🌐 Checking if running on AWS EC2..."
if curl -s --max-time 3 http://***************/latest/meta-data/instance-id &>/dev/null; then
    INSTANCE_ID=$(curl -s http://***************/latest/meta-data/instance-id)
    PUBLIC_IP=$(curl -s http://***************/latest/meta-data/public-ipv4)
    PUBLIC_DNS=$(curl -s http://***************/latest/meta-data/public-hostname)
    REGION=$(curl -s http://***************/latest/meta-data/placement/region)
    
    echo "✅ Running on AWS EC2"
    echo "   Instance ID: $INSTANCE_ID"
    echo "   Public IP: $PUBLIC_IP"
    echo "   Public DNS: $PUBLIC_DNS"
    echo "   Region: $REGION"
else
    echo "❌ Not running on AWS EC2 or metadata service unavailable"
    echo "This script is designed for AWS EC2 instances"
    exit 1
fi

echo ""
echo "🔒 Checking Security Group Configuration..."

# Check if AWS CLI is installed
if command -v aws &> /dev/null; then
    echo "✅ AWS CLI found"
    
    # Try to get security group info
    if aws sts get-caller-identity &>/dev/null; then
        echo "✅ AWS CLI configured"
        
        # Get security groups for this instance
        SECURITY_GROUPS=$(aws ec2 describe-instances \
            --instance-ids $INSTANCE_ID \
            --region $REGION \
            --query 'Reservations[0].Instances[0].SecurityGroups[].GroupId' \
            --output text 2>/dev/null || echo "")
        
        if [[ -n "$SECURITY_GROUPS" ]]; then
            echo "🔍 Security Groups: $SECURITY_GROUPS"
            
            # Check each security group for required ports
            for sg in $SECURITY_GROUPS; do
                echo ""
                echo "📋 Checking Security Group: $sg"
                
                # Check port 80 (HTTP)
                HTTP_RULE=$(aws ec2 describe-security-groups \
                    --group-ids $sg \
                    --region $REGION \
                    --query "SecurityGroups[0].IpPermissions[?FromPort==\`80\` && ToPort==\`80\`]" \
                    --output text 2>/dev/null || echo "")
                
                if [[ -n "$HTTP_RULE" && "$HTTP_RULE" != "None" ]]; then
                    echo "   ✅ Port 80 (HTTP) is open"
                else
                    echo "   ❌ Port 80 (HTTP) is NOT open"
                    echo "      Add rule: HTTP (80) from 0.0.0.0/0"
                fi
                
                # Check port 443 (HTTPS)
                HTTPS_RULE=$(aws ec2 describe-security-groups \
                    --group-ids $sg \
                    --region $REGION \
                    --query "SecurityGroups[0].IpPermissions[?FromPort==\`443\` && ToPort==\`443\`]" \
                    --output text 2>/dev/null || echo "")
                
                if [[ -n "$HTTPS_RULE" && "$HTTPS_RULE" != "None" ]]; then
                    echo "   ✅ Port 443 (HTTPS) is open"
                else
                    echo "   ❌ Port 443 (HTTPS) is NOT open"
                    echo "      Add rule: HTTPS (443) from 0.0.0.0/0"
                fi
                
                # Check port 3000 (Backend API)
                API_RULE=$(aws ec2 describe-security-groups \
                    --group-ids $sg \
                    --region $REGION \
                    --query "SecurityGroups[0].IpPermissions[?FromPort==\`3000\` && ToPort==\`3000\`]" \
                    --output text 2>/dev/null || echo "")
                
                if [[ -n "$API_RULE" && "$API_RULE" != "None" ]]; then
                    echo "   ✅ Port 3000 (Backend API) is open"
                else
                    echo "   ⚠️  Port 3000 (Backend API) is NOT open"
                    echo "      Add rule: Custom TCP (3000) from 0.0.0.0/0"
                fi
            done
        else
            echo "❌ Could not retrieve security group information"
        fi
    else
        echo "⚠️  AWS CLI not configured"
        echo "   Run: aws configure"
    fi
else
    echo "⚠️  AWS CLI not installed"
    echo "   Install with: sudo yum install -y awscli"
fi

echo ""
echo "🌐 Testing Network Connectivity..."

# Test external connectivity
echo "🔍 Testing external connectivity..."
if curl -s --max-time 5 https://google.com &>/dev/null; then
    echo "✅ External internet connectivity working"
else
    echo "❌ External internet connectivity failed"
fi

# Test if ports are accessible from outside
echo ""
echo "🔍 Testing port accessibility..."

# Test port 80
echo "Testing port 80 (HTTP)..."
if timeout 5 bash -c "echo >/dev/tcp/$PUBLIC_IP/80" 2>/dev/null; then
    echo "✅ Port 80 is accessible"
else
    echo "❌ Port 80 is NOT accessible from outside"
fi

# Test port 443
echo "Testing port 443 (HTTPS)..."
if timeout 5 bash -c "echo >/dev/tcp/$PUBLIC_IP/443" 2>/dev/null; then
    echo "✅ Port 443 is accessible"
else
    echo "❌ Port 443 is NOT accessible from outside"
fi

# Test port 3000
echo "Testing port 3000 (Backend API)..."
if timeout 5 bash -c "echo >/dev/tcp/$PUBLIC_IP/3000" 2>/dev/null; then
    echo "✅ Port 3000 is accessible"
else
    echo "❌ Port 3000 is NOT accessible from outside"
fi

echo ""
echo "🐳 Checking Docker Status..."

if command -v docker &> /dev/null; then
    echo "✅ Docker is installed"
    
    if sudo systemctl is-active --quiet docker; then
        echo "✅ Docker service is running"
        
        # Check Docker version
        DOCKER_VERSION=$(docker --version)
        echo "   Version: $DOCKER_VERSION"
        
        # Check if user is in docker group
        if groups | grep -q docker; then
            echo "✅ User is in docker group"
        else
            echo "⚠️  User is NOT in docker group"
            echo "   Run: sudo usermod -a -G docker $(whoami)"
            echo "   Then log out and back in"
        fi
    else
        echo "❌ Docker service is NOT running"
        echo "   Run: sudo systemctl start docker"
    fi
else
    echo "❌ Docker is NOT installed"
    echo "   Run: ./setup-amazon-linux.sh"
fi

# Check Docker Compose
if command -v docker-compose &> /dev/null; then
    echo "✅ Docker Compose is installed"
    COMPOSE_VERSION=$(docker-compose --version)
    echo "   Version: $COMPOSE_VERSION"
else
    echo "❌ Docker Compose is NOT installed"
    echo "   Run: ./setup-amazon-linux.sh"
fi

echo ""
echo "📋 Summary and Recommendations:"
echo "==============================="

# Generate AWS CLI commands for security group setup
if [[ -n "$SECURITY_GROUPS" ]]; then
    echo ""
    echo "🔧 To fix security group issues, run these AWS CLI commands:"
    echo ""
    for sg in $SECURITY_GROUPS; do
        echo "# For Security Group: $sg"
        echo "aws ec2 authorize-security-group-ingress --group-id $sg --protocol tcp --port 80 --cidr 0.0.0.0/0 --region $REGION"
        echo "aws ec2 authorize-security-group-ingress --group-id $sg --protocol tcp --port 443 --cidr 0.0.0.0/0 --region $REGION"
        echo "aws ec2 authorize-security-group-ingress --group-id $sg --protocol tcp --port 3000 --cidr 0.0.0.0/0 --region $REGION"
        echo ""
    done
fi

echo "🌐 Your application URLs will be:"
echo "   HTTP:  http://$PUBLIC_DNS"
echo "   HTTPS: https://$PUBLIC_DNS"
echo ""
echo "📋 Next steps:"
echo "   1. Fix any security group issues above"
echo "   2. Run: ./setup-amazon-linux.sh (if not done)"
echo "   3. Run: ./deploy-https.sh"
echo ""
echo "✅ AWS setup check complete!"
