#!/bin/bash

# Stop Tuchanga services
# This script stops all services gracefully

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Configuration
PROJECT_DIR="/opt/tuchanga"

# Function to check if command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Check prerequisites
check_prerequisites() {
    if ! command_exists docker; then
        print_error "Docker no está instalado"
        exit 1
    fi
    
    if ! command_exists docker-compose; then
        print_error "Docker Compose no está instalado"
        exit 1
    fi
    
    if [[ ! -d "$PROJECT_DIR" ]]; then
        print_error "Directorio del proyecto no encontrado: $PROJECT_DIR"
        exit 1
    fi
}

# Stop Docker containers
stop_containers() {
    print_status "Deteniendo contenedores Docker..."
    
    cd "$PROJECT_DIR"
    
    # Stop containers gracefully
    if docker-compose ps -q | grep -q .; then
        print_status "Deteniendo contenedores con docker-compose..."
        docker-compose down --remove-orphans
        
        # Wait for containers to stop
        sleep 5
        
        # Check if any containers are still running
        local remaining_containers=$(docker ps -q --filter "name=tuchanga")
        if [[ -n "$remaining_containers" ]]; then
            print_warning "Algunos contenedores aún están ejecutándose, forzando detención..."
            echo "$remaining_containers" | xargs docker stop
            
            # Wait a bit more
            sleep 3
            
            # Force kill if still running
            remaining_containers=$(docker ps -q --filter "name=tuchanga")
            if [[ -n "$remaining_containers" ]]; then
                print_warning "Forzando terminación de contenedores..."
                echo "$remaining_containers" | xargs docker kill
            fi
        fi
        
        print_success "Contenedores Docker detenidos"
    else
        print_success "No hay contenedores de Tuchanga ejecutándose"
    fi
}

# Stop Nginx
stop_nginx() {
    print_status "Deteniendo Nginx..."
    
    if systemctl is-active --quiet nginx; then
        sudo systemctl stop nginx
        
        # Wait for Nginx to stop
        sleep 2
        
        if ! systemctl is-active --quiet nginx; then
            print_success "Nginx detenido exitosamente"
        else
            print_error "Error al detener Nginx"
            return 1
        fi
    else
        print_success "Nginx no estaba ejecutándose"
    fi
}

# Stop specific service
stop_specific_service() {
    local service="$1"
    
    case "$service" in
        "nginx")
            stop_nginx
            ;;
        "containers"|"docker")
            stop_containers
            ;;
        "backend")
            print_status "Deteniendo solo el backend..."
            cd "$PROJECT_DIR"
            docker-compose stop backend
            print_success "Backend detenido"
            ;;
        "database"|"db")
            print_status "Deteniendo solo la base de datos..."
            cd "$PROJECT_DIR"
            docker-compose stop postgres
            print_success "Base de datos detenida"
            ;;
        "frontend")
            print_status "El frontend es servido por Nginx, detén Nginx en su lugar"
            ;;
        *)
            print_error "Servicio desconocido: $service"
            return 1
            ;;
    esac
}

# Show stop menu
show_stop_menu() {
    echo ""
    print_status "Selecciona qué detener:"
    echo "1. 🛑 Detener todo (recomendado)"
    echo "2. 🌐 Detener solo Nginx"
    echo "3. 🐳 Detener solo contenedores Docker"
    echo "4. ⚙️  Detener solo backend"
    echo "5. 🗄️  Detener solo base de datos"
    echo "0. ❌ Cancelar"
    echo ""
}

# Create maintenance page
create_maintenance_page() {
    print_status "Creando página de mantenimiento..."
    
    local web_dir="/var/www/tuchanga"
    local maintenance_file="$web_dir/maintenance.html"
    
    # Create maintenance page
    sudo tee "$maintenance_file" > /dev/null << 'EOF'
<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Tuchanga - Mantenimiento</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            margin: 0;
            padding: 0;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
        }
        .container {
            text-align: center;
            background: rgba(255, 255, 255, 0.1);
            padding: 3rem;
            border-radius: 20px;
            backdrop-filter: blur(10px);
            box-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.37);
            border: 1px solid rgba(255, 255, 255, 0.18);
            max-width: 500px;
            margin: 2rem;
        }
        h1 {
            font-size: 2.5rem;
            margin-bottom: 1rem;
            color: #fff;
        }
        p {
            font-size: 1.2rem;
            margin-bottom: 2rem;
            opacity: 0.9;
        }
        .spinner {
            border: 4px solid rgba(255, 255, 255, 0.3);
            border-radius: 50%;
            border-top: 4px solid #fff;
            width: 50px;
            height: 50px;
            animation: spin 1s linear infinite;
            margin: 2rem auto;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        .info {
            font-size: 0.9rem;
            opacity: 0.7;
            margin-top: 2rem;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 Mantenimiento</h1>
        <p>Tuchanga está temporalmente fuera de servicio por mantenimiento.</p>
        <div class="spinner"></div>
        <p>Volveremos pronto con mejoras.</p>
        <div class="info">
            Si eres el administrador, puedes iniciar los servicios ejecutando:<br>
            <code>./start-application.sh</code>
        </div>
    </div>
</body>
</html>
EOF
    
    # Set permissions
    sudo chown www-data:www-data "$maintenance_file"
    sudo chmod 644 "$maintenance_file"
    
    # Backup current index.html and replace with maintenance page
    if [[ -f "$web_dir/index.html" ]]; then
        sudo mv "$web_dir/index.html" "$web_dir/index.html.backup"
    fi
    sudo cp "$maintenance_file" "$web_dir/index.html"
    
    print_success "Página de mantenimiento activada"
}

# Restore original page
restore_original_page() {
    local web_dir="/var/www/tuchanga"
    
    if [[ -f "$web_dir/index.html.backup" ]]; then
        print_status "Restaurando página original..."
        sudo mv "$web_dir/index.html.backup" "$web_dir/index.html"
        sudo rm -f "$web_dir/maintenance.html"
        print_success "Página original restaurada"
    fi
}

# Show service status after stopping
show_status() {
    print_status "Estado de los servicios después de la detención:"
    echo ""
    
    # Nginx status
    if systemctl is-active --quiet nginx; then
        echo "⚠️  Nginx: Aún ejecutándose"
    else
        echo "✅ Nginx: Detenido"
    fi
    
    # Container status
    local running_containers=$(docker ps -q --filter "name=tuchanga" | wc -l)
    if [[ $running_containers -eq 0 ]]; then
        echo "✅ Contenedores Docker: Todos detenidos"
    else
        echo "⚠️  Contenedores Docker: $running_containers aún ejecutándose"
        docker ps --format "table {{.Names}}\t{{.Status}}" | grep tuchanga
    fi
    
    echo ""
    
    # Show resource usage after stopping
    print_status "Uso de recursos después de la detención:"
    echo "💾 Memoria libre: $(free -h | awk 'NR==2{print $7}')"
    echo "🖥️  Carga del sistema: $(uptime | awk -F'load average:' '{print $2}')"
    
    echo ""
}

# Main function
main() {
    print_status "Deteniendo servicios de Tuchanga..."
    echo ""
    
    check_prerequisites
    
    # If no arguments provided, show menu
    if [[ $# -eq 0 ]]; then
        show_stop_menu
        read -p "Ingresa tu opción [0-5]: " choice
        
        case $choice in
            1)
                print_status "Deteniendo todos los servicios..."
                create_maintenance_page
                stop_containers
                stop_nginx
                ;;
            2)
                create_maintenance_page
                stop_nginx
                ;;
            3)
                stop_containers
                ;;
            4)
                stop_specific_service "backend"
                ;;
            5)
                stop_specific_service "database"
                ;;
            0)
                print_status "Operación cancelada"
                exit 0
                ;;
            *)
                print_error "Opción inválida"
                exit 1
                ;;
        esac
    else
        # Handle command line arguments
        case "$1" in
            "all"|"todo")
                create_maintenance_page
                stop_containers
                stop_nginx
                ;;
            "nginx")
                create_maintenance_page
                stop_nginx
                ;;
            "containers"|"docker")
                stop_containers
                ;;
            "backend"|"database"|"db"|"frontend")
                stop_specific_service "$1"
                ;;
            *)
                print_error "Argumento inválido: $1"
                echo "Uso: $0 [all|nginx|containers|backend|database]"
                exit 1
                ;;
        esac
    fi
    
    # Wait a moment for services to stop completely
    sleep 3
    
    show_status
    
    print_success "¡Detención completada!"
    echo ""
    print_status "Para reiniciar los servicios:"
    echo "- Iniciar aplicación: ./start-application.sh"
    echo "- Reiniciar servicios: ./restart-services.sh"
    echo "- Ver estado: ./check-status.sh"
    echo ""
    
    if systemctl is-active --quiet nginx; then
        print_status "Nginx sigue ejecutándose y mostrará la página de mantenimiento"
    fi
}

# Run main function
main "$@"
