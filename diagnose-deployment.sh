#!/bin/bash

# Deployment Diagnosis Script for Tuchanga Job Platform
# This script helps diagnose deployment issues on AWS EC2

set -e

echo "🔍 Tuchanga Deployment Diagnosis"
echo "================================="
echo ""

# Check if running as root or with sudo
if [[ $EUID -eq 0 ]]; then
    echo "⚠️  Running as root - this is not recommended for production"
fi

echo "📋 System Information:"
echo "----------------------"
echo "OS: $(cat /etc/os-release | grep PRETTY_NAME | cut -d'"' -f2)"
echo "Hostname: $(hostname)"
echo "Public IP: $(curl -s http://checkip.amazonaws.com/ || echo 'Unable to get public IP')"
echo "Date: $(date)"
echo ""

echo "🐳 Docker Status:"
echo "-----------------"
if command -v docker &> /dev/null; then
    echo "✅ Docker is installed"
    echo "Docker version: $(docker --version)"
    
    if docker info &> /dev/null; then
        echo "✅ Docker daemon is running"
    else
        echo "❌ Docker daemon is not running"
        echo "Try: sudo systemctl start docker"
        exit 1
    fi
else
    echo "❌ Docker is not installed"
    exit 1
fi

if command -v docker-compose &> /dev/null; then
    echo "✅ Docker Compose is installed"
    echo "Docker Compose version: $(docker-compose --version)"
else
    echo "❌ Docker Compose is not installed"
fi

echo ""

echo "📦 Container Status:"
echo "-------------------"
docker ps --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}"
echo ""

echo "🔍 Container Health:"
echo "-------------------"
for container in job-platform-postgres job-platform-backend job-platform-frontend; do
    if docker ps --filter "name=$container" --format "{{.Names}}" | grep -q "$container"; then
        health=$(docker inspect --format='{{.State.Health.Status}}' $container 2>/dev/null || echo "no-healthcheck")
        echo "$container: $health"
    else
        echo "$container: not running"
    fi
done
echo ""

echo "🌐 Network Connectivity:"
echo "------------------------"

# Check if ports are listening
echo "Checking ports:"
for port in 80 443 3000 5432; do
    if netstat -tuln | grep -q ":$port "; then
        echo "✅ Port $port is listening"
    else
        echo "❌ Port $port is not listening"
    fi
done
echo ""

# Test internal container connectivity
echo "Testing internal connectivity:"
if docker ps --filter "name=job-platform-backend" --format "{{.Names}}" | grep -q "job-platform-backend"; then
    backend_health=$(docker exec job-platform-backend curl -s -o /dev/null -w "%{http_code}" http://localhost:3000/health 2>/dev/null || echo "failed")
    if [[ "$backend_health" == "200" ]]; then
        echo "✅ Backend health check: OK"
    else
        echo "❌ Backend health check: Failed ($backend_health)"
    fi
else
    echo "❌ Backend container not running"
fi

if docker ps --filter "name=job-platform-frontend" --format "{{.Names}}" | grep -q "job-platform-frontend"; then
    frontend_health=$(docker exec job-platform-frontend curl -s -o /dev/null -w "%{http_code}" http://localhost:80 2>/dev/null || echo "failed")
    if [[ "$frontend_health" == "200" ]]; then
        echo "✅ Frontend health check: OK"
    else
        echo "❌ Frontend health check: Failed ($frontend_health)"
    fi
else
    echo "❌ Frontend container not running"
fi
echo ""

echo "🔐 SSL Certificate Status:"
echo "--------------------------"
if [[ -f "./ssl/cert.pem" ]]; then
    echo "✅ SSL certificate found"
    echo "Certificate details:"
    openssl x509 -in ./ssl/cert.pem -text -noout | grep -E "(Subject:|Not Before|Not After|DNS:)" || echo "Unable to read certificate details"
else
    echo "❌ SSL certificate not found at ./ssl/cert.pem"
fi
echo ""

echo "🔥 Firewall Status:"
echo "------------------"
if command -v ufw &> /dev/null; then
    echo "UFW status:"
    sudo ufw status
elif command -v firewall-cmd &> /dev/null; then
    echo "Firewalld status:"
    sudo firewall-cmd --list-all
else
    echo "No common firewall detected"
fi
echo ""

echo "☁️ AWS Security Group Check:"
echo "----------------------------"
echo "Make sure your EC2 Security Group allows:"
echo "- HTTP (port 80) from 0.0.0.0/0"
echo "- HTTPS (port 443) from 0.0.0.0/0"
echo "- Custom TCP (port 3000) from 0.0.0.0/0 (if accessing backend directly)"
echo ""

echo "🌍 DNS Configuration:"
echo "---------------------"
echo "Testing DNS resolution for your domain:"
domain="tuchanga.com"
if nslookup $domain &> /dev/null; then
    resolved_ip=$(nslookup $domain | grep -A1 "Name:" | tail -1 | awk '{print $2}')
    public_ip=$(curl -s http://checkip.amazonaws.com/ || echo 'unknown')
    echo "Domain $domain resolves to: $resolved_ip"
    echo "Your public IP is: $public_ip"
    
    if [[ "$resolved_ip" == "$public_ip" ]]; then
        echo "✅ DNS is correctly pointing to this server"
    else
        echo "❌ DNS is not pointing to this server"
        echo "Update your DNS A record to point to: $public_ip"
    fi
else
    echo "❌ Unable to resolve domain $domain"
fi
echo ""

echo "📊 Resource Usage:"
echo "------------------"
echo "Memory usage:"
free -h
echo ""
echo "Disk usage:"
df -h
echo ""
echo "CPU usage:"
top -bn1 | grep "Cpu(s)" || echo "Unable to get CPU usage"
echo ""

echo "📝 Container Logs (last 10 lines):"
echo "-----------------------------------"
for container in job-platform-postgres job-platform-backend job-platform-frontend; do
    if docker ps --filter "name=$container" --format "{{.Names}}" | grep -q "$container"; then
        echo "=== $container ==="
        docker logs --tail 10 $container 2>&1 | head -10
        echo ""
    fi
done

echo "🔧 Troubleshooting Suggestions:"
echo "-------------------------------"
echo "1. If containers are not running:"
echo "   docker-compose up -d"
echo ""
echo "2. If SSL certificate is missing:"
echo "   ./setup-https.sh"
echo ""
echo "3. If ports are not accessible:"
echo "   - Check AWS Security Group settings"
echo "   - Check local firewall (ufw/firewalld)"
echo ""
echo "4. If DNS is not resolving:"
echo "   - Update A record in your domain provider"
echo "   - Wait for DNS propagation (up to 48 hours)"
echo ""
echo "5. View detailed logs:"
echo "   docker-compose logs -f [service-name]"
echo ""
echo "6. Restart services:"
echo "   docker-compose restart"
echo ""

echo "✅ Diagnosis complete!"
echo ""
echo "📞 If you need help, share this output with your support team."
