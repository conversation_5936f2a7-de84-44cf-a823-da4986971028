# Multi-stage Dockerfile optimized for AWS ECS deployment
# Stage 1: Build the React application
FROM node:22-alpine AS builder

WORKDIR /app

# Copy package files
COPY package*.json ./

# Configure npm for better performance and install dependencies
RUN npm config set maxsockets 1 && \
    npm config set progress false && \
    npm ci --no-audit --no-fund --prefer-offline

# Copy source code
COPY . .

# Build with memory constraints for ECS
RUN NODE_OPTIONS="--max-old-space-size=1024" npm run build

# Stage 2: Serve with nginx optimized for ECS
FROM nginx:alpine

# Install envsubst for environment variable substitution
RUN apk add --no-cache gettext

# Copy built files from builder stage
COPY --from=builder /app/dist /usr/share/nginx/html

# Copy ECS-optimized nginx configuration template
COPY nginx-ecs.conf /etc/nginx/templates/default.conf.template

# Create startup script for environment variable substitution
RUN echo '#!/bin/sh' > /docker-entrypoint.d/40-substitute-env.sh && \
    echo 'envsubst "\$BACKEND_URL" < /etc/nginx/templates/default.conf.template > /etc/nginx/conf.d/default.conf' >> /docker-entrypoint.d/40-substitute-env.sh && \
    chmod +x /docker-entrypoint.d/40-substitute-env.sh

# Health check for ECS
HEALTHCHECK --interval=30s --timeout=5s --start-period=10s --retries=3 \
    CMD curl -f http://localhost:80/health || exit 1

# Expose port 80 (ALB will handle SSL)
EXPOSE 80

# Start nginx
CMD ["nginx", "-g", "daemon off;"]
