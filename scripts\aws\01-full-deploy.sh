#!/bin/bash

# TuChanga - Full Infrastructure Deployment Script
# This script creates everything from scratch: VPC, RDS, ECS, ECR, ALB

set -e

# Configuration
AWS_REGION="sa-east-1"
PROJECT_NAME="tuchanga"
VPC_CIDR="10.0.0.0/16"
PUBLIC_SUBNET_1_CIDR="********/24"
PUBLIC_SUBNET_2_CIDR="********/24"
PRIVATE_SUBNET_1_CIDR="********/24"
PRIVATE_SUBNET_2_CIDR="********/24"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Get AWS Account ID
AWS_ACCOUNT_ID=$(aws sts get-caller-identity --query Account --output text)

echo -e "${GREEN}🚀 Starting TuChanga Full Infrastructure Deployment${NC}"
echo -e "${BLUE}📍 Region: $AWS_REGION${NC}"
echo -e "${BLUE}🏗️  Account: $AWS_ACCOUNT_ID${NC}"

# Function to check if resource exists
resource_exists() {
    local resource_type=$1
    local resource_name=$2
    
    case $resource_type in
        "vpc")
            aws ec2 describe-vpcs --filters "Name=tag:Name,Values=$resource_name" --query 'Vpcs[0].VpcId' --output text 2>/dev/null | grep -v "None"
            ;;
        "ecr")
            aws ecr describe-repositories --repository-names $resource_name --query 'repositories[0].repositoryName' --output text 2>/dev/null
            ;;
        "cluster")
            aws ecs describe-clusters --clusters $resource_name --query 'clusters[0].clusterName' --output text 2>/dev/null | grep -v "None"
            ;;
        "db-cluster")
            aws rds describe-db-clusters --db-cluster-identifier $resource_name --query 'DBClusters[0].DBClusterIdentifier' --output text 2>/dev/null
            ;;
    esac
}

# 1. Create VPC and Networking
echo -e "${YELLOW}🌐 Creating VPC and networking infrastructure...${NC}"

VPC_ID=$(resource_exists "vpc" "$PROJECT_NAME-vpc")
if [ -z "$VPC_ID" ] || [ "$VPC_ID" = "None" ]; then
    echo "Creating VPC..."
    VPC_ID=$(aws ec2 create-vpc --cidr-block $VPC_CIDR --query 'Vpc.VpcId' --output text)
    aws ec2 create-tags --resources $VPC_ID --tags Key=Name,Value=$PROJECT_NAME-vpc
    
    # Enable DNS hostnames
    aws ec2 modify-vpc-attribute --vpc-id $VPC_ID --enable-dns-hostnames
    aws ec2 modify-vpc-attribute --vpc-id $VPC_ID --enable-dns-support
else
    echo "VPC already exists: $VPC_ID"
fi

# Get availability zones
AZ1=$(aws ec2 describe-availability-zones --query 'AvailabilityZones[0].ZoneName' --output text)
AZ2=$(aws ec2 describe-availability-zones --query 'AvailabilityZones[1].ZoneName' --output text)

# Create Internet Gateway
IGW_ID=$(aws ec2 describe-internet-gateways --filters "Name=attachment.vpc-id,Values=$VPC_ID" --query 'InternetGateways[0].InternetGatewayId' --output text)
if [ "$IGW_ID" = "None" ] || [ -z "$IGW_ID" ]; then
    echo "Creating Internet Gateway..."
    IGW_ID=$(aws ec2 create-internet-gateway --query 'InternetGateway.InternetGatewayId' --output text)
    aws ec2 attach-internet-gateway --vpc-id $VPC_ID --internet-gateway-id $IGW_ID
    aws ec2 create-tags --resources $IGW_ID --tags Key=Name,Value=$PROJECT_NAME-igw
fi

# Create Subnets
echo "Creating subnets..."
PUBLIC_SUBNET_1_ID=$(aws ec2 create-subnet --vpc-id $VPC_ID --cidr-block $PUBLIC_SUBNET_1_CIDR --availability-zone $AZ1 --query 'Subnet.SubnetId' --output text 2>/dev/null || aws ec2 describe-subnets --filters "Name=vpc-id,Values=$VPC_ID" "Name=cidr-block,Values=$PUBLIC_SUBNET_1_CIDR" --query 'Subnets[0].SubnetId' --output text)
PUBLIC_SUBNET_2_ID=$(aws ec2 create-subnet --vpc-id $VPC_ID --cidr-block $PUBLIC_SUBNET_2_CIDR --availability-zone $AZ2 --query 'Subnet.SubnetId' --output text 2>/dev/null || aws ec2 describe-subnets --filters "Name=vpc-id,Values=$VPC_ID" "Name=cidr-block,Values=$PUBLIC_SUBNET_2_CIDR" --query 'Subnets[0].SubnetId' --output text)
PRIVATE_SUBNET_1_ID=$(aws ec2 create-subnet --vpc-id $VPC_ID --cidr-block $PRIVATE_SUBNET_1_CIDR --availability-zone $AZ1 --query 'Subnet.SubnetId' --output text 2>/dev/null || aws ec2 describe-subnets --filters "Name=vpc-id,Values=$VPC_ID" "Name=cidr-block,Values=$PRIVATE_SUBNET_1_CIDR" --query 'Subnets[0].SubnetId' --output text)
PRIVATE_SUBNET_2_ID=$(aws ec2 create-subnet --vpc-id $VPC_ID --cidr-block $PRIVATE_SUBNET_2_CIDR --availability-zone $AZ2 --query 'Subnet.SubnetId' --output text 2>/dev/null || aws ec2 describe-subnets --filters "Name=vpc-id,Values=$VPC_ID" "Name=cidr-block,Values=$PRIVATE_SUBNET_2_CIDR" --query 'Subnets[0].SubnetId' --output text)

# Tag subnets
aws ec2 create-tags --resources $PUBLIC_SUBNET_1_ID --tags Key=Name,Value=$PROJECT_NAME-public-1
aws ec2 create-tags --resources $PUBLIC_SUBNET_2_ID --tags Key=Name,Value=$PROJECT_NAME-public-2
aws ec2 create-tags --resources $PRIVATE_SUBNET_1_ID --tags Key=Name,Value=$PROJECT_NAME-private-1
aws ec2 create-tags --resources $PRIVATE_SUBNET_2_ID --tags Key=Name,Value=$PROJECT_NAME-private-2

# Enable auto-assign public IP for public subnets
aws ec2 modify-subnet-attribute --subnet-id $PUBLIC_SUBNET_1_ID --map-public-ip-on-launch
aws ec2 modify-subnet-attribute --subnet-id $PUBLIC_SUBNET_2_ID --map-public-ip-on-launch

# Create Route Table for public subnets
PUBLIC_RT_ID=$(aws ec2 create-route-table --vpc-id $VPC_ID --query 'RouteTable.RouteTableId' --output text 2>/dev/null || aws ec2 describe-route-tables --filters "Name=vpc-id,Values=$VPC_ID" "Name=tag:Name,Values=$PROJECT_NAME-public-rt" --query 'RouteTables[0].RouteTableId' --output text)
aws ec2 create-tags --resources $PUBLIC_RT_ID --tags Key=Name,Value=$PROJECT_NAME-public-rt
aws ec2 create-route --route-table-id $PUBLIC_RT_ID --destination-cidr-block 0.0.0.0/0 --gateway-id $IGW_ID 2>/dev/null || true
aws ec2 associate-route-table --subnet-id $PUBLIC_SUBNET_1_ID --route-table-id $PUBLIC_RT_ID 2>/dev/null || true
aws ec2 associate-route-table --subnet-id $PUBLIC_SUBNET_2_ID --route-table-id $PUBLIC_RT_ID 2>/dev/null || true

echo -e "${GREEN}✅ VPC infrastructure created${NC}"
echo "VPC ID: $VPC_ID"
echo "Public Subnets: $PUBLIC_SUBNET_1_ID, $PUBLIC_SUBNET_2_ID"
echo "Private Subnets: $PRIVATE_SUBNET_1_ID, $PRIVATE_SUBNET_2_ID"

# 2. Create Security Groups
echo -e "${YELLOW}🔒 Creating security groups...${NC}"

# ALB Security Group
ALB_SG_ID=$(aws ec2 describe-security-groups --filters "Name=group-name,Values=$PROJECT_NAME-alb-sg" "Name=vpc-id,Values=$VPC_ID" --query 'SecurityGroups[0].GroupId' --output text 2>/dev/null)
if [ "$ALB_SG_ID" = "None" ] || [ -z "$ALB_SG_ID" ]; then
    ALB_SG_ID=$(aws ec2 create-security-group --group-name $PROJECT_NAME-alb-sg --description "Security group for ALB" --vpc-id $VPC_ID --query 'GroupId' --output text)
    aws ec2 authorize-security-group-ingress --group-id $ALB_SG_ID --protocol tcp --port 80 --cidr 0.0.0.0/0
    aws ec2 authorize-security-group-ingress --group-id $ALB_SG_ID --protocol tcp --port 443 --cidr 0.0.0.0/0
fi

# ECS Security Group
ECS_SG_ID=$(aws ec2 describe-security-groups --filters "Name=group-name,Values=$PROJECT_NAME-ecs-sg" "Name=vpc-id,Values=$VPC_ID" --query 'SecurityGroups[0].GroupId' --output text 2>/dev/null)
if [ "$ECS_SG_ID" = "None" ] || [ -z "$ECS_SG_ID" ]; then
    ECS_SG_ID=$(aws ec2 create-security-group --group-name $PROJECT_NAME-ecs-sg --description "Security group for ECS tasks" --vpc-id $VPC_ID --query 'GroupId' --output text)
    aws ec2 authorize-security-group-ingress --group-id $ECS_SG_ID --protocol tcp --port 3000 --source-group $ALB_SG_ID
    aws ec2 authorize-security-group-ingress --group-id $ECS_SG_ID --protocol tcp --port 80 --source-group $ALB_SG_ID
fi

# RDS Security Group
RDS_SG_ID=$(aws ec2 describe-security-groups --filters "Name=group-name,Values=$PROJECT_NAME-rds-sg" "Name=vpc-id,Values=$VPC_ID" --query 'SecurityGroups[0].GroupId' --output text 2>/dev/null)
if [ "$RDS_SG_ID" = "None" ] || [ -z "$RDS_SG_ID" ]; then
    RDS_SG_ID=$(aws ec2 create-security-group --group-name $PROJECT_NAME-rds-sg --description "Security group for RDS" --vpc-id $VPC_ID --query 'GroupId' --output text)
    aws ec2 authorize-security-group-ingress --group-id $RDS_SG_ID --protocol tcp --port 5432 --source-group $ECS_SG_ID
fi

echo -e "${GREEN}✅ Security groups created${NC}"

# 3. Create ECR Repositories
echo -e "${YELLOW}📦 Creating ECR repositories...${NC}"

for repo in "$PROJECT_NAME-backend" "$PROJECT_NAME-frontend"; do
    if ! resource_exists "ecr" "$repo" >/dev/null 2>&1; then
        echo "Creating ECR repository: $repo"
        aws ecr create-repository --repository-name $repo --region $AWS_REGION
    else
        echo "ECR repository already exists: $repo"
    fi
done

echo -e "${GREEN}✅ ECR repositories ready${NC}"

# 4. Create RDS Aurora Cluster
echo -e "${YELLOW}🗄️  Creating Aurora PostgreSQL cluster...${NC}"

DB_CLUSTER_ID="$PROJECT_NAME-aurora-cluster"
if ! resource_exists "db-cluster" "$DB_CLUSTER_ID" >/dev/null 2>&1; then
    echo "Creating Aurora cluster..."

    # Create DB subnet group
    aws rds create-db-subnet-group \
        --db-subnet-group-name $PROJECT_NAME-subnet-group \
        --db-subnet-group-description "Subnet group for TuChanga Aurora cluster" \
        --subnet-ids $PRIVATE_SUBNET_1_ID $PRIVATE_SUBNET_2_ID 2>/dev/null || true

    # Create Aurora cluster
    aws rds create-db-cluster \
        --db-cluster-identifier $DB_CLUSTER_ID \
        --engine aurora-postgresql \
        --engine-version 15.4 \
        --master-username postgres \
        --master-user-password "TuChanga2024SecureDB!" \
        --database-name tuchanga \
        --vpc-security-group-ids $RDS_SG_ID \
        --db-subnet-group-name $PROJECT_NAME-subnet-group \
        --backup-retention-period 7 \
        --preferred-backup-window "03:00-04:00" \
        --preferred-maintenance-window "sun:04:00-sun:05:00" \
        --storage-encrypted

    # Create Aurora instance
    aws rds create-db-instance \
        --db-instance-identifier $PROJECT_NAME-aurora-instance-1 \
        --db-instance-class db.t3.medium \
        --engine aurora-postgresql \
        --db-cluster-identifier $DB_CLUSTER_ID

    echo "Waiting for Aurora cluster to be available..."
    aws rds wait db-cluster-available --db-cluster-identifier $DB_CLUSTER_ID
else
    echo "Aurora cluster already exists: $DB_CLUSTER_ID"
fi

# Get Aurora endpoint
AURORA_ENDPOINT=$(aws rds describe-db-clusters --db-cluster-identifier $DB_CLUSTER_ID --query 'DBClusters[0].Endpoint' --output text)
echo -e "${GREEN}✅ Aurora cluster ready${NC}"
echo "Aurora endpoint: $AURORA_ENDPOINT"

# 5. Create Secrets Manager secrets
echo -e "${YELLOW}🔐 Creating secrets in Secrets Manager...${NC}"

# Database password
aws secretsmanager create-secret \
    --name "$PROJECT_NAME/db-password" \
    --description "Database password for TuChanga" \
    --secret-string "TuChanga2024SecureDB!" 2>/dev/null || \
aws secretsmanager update-secret \
    --secret-id "$PROJECT_NAME/db-password" \
    --secret-string "TuChanga2024SecureDB!" 2>/dev/null || true

# JWT Secret
aws secretsmanager create-secret \
    --name "$PROJECT_NAME/jwt-secret" \
    --description "JWT secret for TuChanga" \
    --secret-string "TuChanga2024SuperSecretJWTKeyChangeInProduction" 2>/dev/null || \
aws secretsmanager update-secret \
    --secret-id "$PROJECT_NAME/jwt-secret" \
    --secret-string "TuChanga2024SuperSecretJWTKeyChangeInProduction" 2>/dev/null || true

# Database URL
DATABASE_URL="*****************************************************************/tuchanga"
aws secretsmanager create-secret \
    --name "$PROJECT_NAME/database-url" \
    --description "Complete database URL for TuChanga" \
    --secret-string "$DATABASE_URL" 2>/dev/null || \
aws secretsmanager update-secret \
    --secret-id "$PROJECT_NAME/database-url" \
    --secret-string "$DATABASE_URL" 2>/dev/null || true

echo -e "${GREEN}✅ Secrets created${NC}"

# 6. Create ECS Cluster
echo -e "${YELLOW}🐳 Creating ECS cluster...${NC}"

CLUSTER_NAME="$PROJECT_NAME-cluster"
if ! resource_exists "cluster" "$CLUSTER_NAME" >/dev/null 2>&1; then
    aws ecs create-cluster --cluster-name $CLUSTER_NAME --capacity-providers FARGATE --default-capacity-provider-strategy capacityProvider=FARGATE,weight=1
else
    echo "ECS cluster already exists: $CLUSTER_NAME"
fi

echo -e "${GREEN}✅ ECS cluster ready${NC}"

# 7. Create IAM roles for ECS
echo -e "${YELLOW}👤 Creating IAM roles...${NC}"

# ECS Task Execution Role
EXECUTION_ROLE_NAME="$PROJECT_NAME-ecs-execution-role"
if ! aws iam get-role --role-name $EXECUTION_ROLE_NAME >/dev/null 2>&1; then
    aws iam create-role \
        --role-name $EXECUTION_ROLE_NAME \
        --assume-role-policy-document '{
            "Version": "2012-10-17",
            "Statement": [
                {
                    "Effect": "Allow",
                    "Principal": {
                        "Service": "ecs-tasks.amazonaws.com"
                    },
                    "Action": "sts:AssumeRole"
                }
            ]
        }'

    aws iam attach-role-policy \
        --role-name $EXECUTION_ROLE_NAME \
        --policy-arn arn:aws:iam::aws:policy/service-role/AmazonECSTaskExecutionRolePolicy
fi

# ECS Task Role
TASK_ROLE_NAME="$PROJECT_NAME-ecs-task-role"
if ! aws iam get-role --role-name $TASK_ROLE_NAME >/dev/null 2>&1; then
    aws iam create-role \
        --role-name $TASK_ROLE_NAME \
        --assume-role-policy-document '{
            "Version": "2012-10-17",
            "Statement": [
                {
                    "Effect": "Allow",
                    "Principal": {
                        "Service": "ecs-tasks.amazonaws.com"
                    },
                    "Action": "sts:AssumeRole"
                }
            ]
        }'

    # Create and attach policy for Secrets Manager access
    aws iam put-role-policy \
        --role-name $TASK_ROLE_NAME \
        --policy-name SecretsManagerAccess \
        --policy-document '{
            "Version": "2012-10-17",
            "Statement": [
                {
                    "Effect": "Allow",
                    "Action": [
                        "secretsmanager:GetSecretValue"
                    ],
                    "Resource": [
                        "arn:aws:secretsmanager:'$AWS_REGION':'$AWS_ACCOUNT_ID':secret:'$PROJECT_NAME'/*"
                    ]
                }
            ]
        }'
fi

echo -e "${GREEN}✅ IAM roles created${NC}"

# 8. Create CloudWatch Log Groups
echo -e "${YELLOW}📊 Creating CloudWatch log groups...${NC}"

aws logs create-log-group --log-group-name "/ecs/$PROJECT_NAME-backend" 2>/dev/null || true
aws logs create-log-group --log-group-name "/ecs/$PROJECT_NAME-frontend" 2>/dev/null || true

echo -e "${GREEN}✅ Log groups created${NC}"

# 9. Deploy containers
echo -e "${YELLOW}🚀 Building and deploying containers...${NC}"

# Call the deployment script
cd "$(dirname "$0")"
./02-deploy-containers.sh

echo -e "${GREEN}🎉 Full deployment completed successfully!${NC}"
echo -e "${BLUE}📋 Summary:${NC}"
echo "VPC ID: $VPC_ID"
echo "Aurora Endpoint: $AURORA_ENDPOINT"
echo "ECS Cluster: $CLUSTER_NAME"
echo ""
echo -e "${YELLOW}🔗 Next steps:${NC}"
echo "1. Create an Application Load Balancer"
echo "2. Configure your domain to point to the ALB"
echo "3. Set up SSL certificate in AWS Certificate Manager"
