#!/bin/bash

# Pre-installation check for Tuchanga EC2 deployment
# This script verifies system requirements and prerequisites

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
NC='\033[0m'

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_header() {
    echo -e "${PURPLE}================================${NC}"
    echo -e "${PURPLE}$1${NC}"
    echo -e "${PURPLE}================================${NC}"
}

# Check system requirements
check_system_requirements() {
    print_header "VERIFICACIÓN DE REQUISITOS DEL SISTEMA"

    local critical_issues=0
    local warnings=0

    # Check OS
    if [[ -f /etc/os-release ]]; then
        local os_info=$(grep "PRETTY_NAME" /etc/os-release | cut -d'"' -f2)
        echo "🖥️  Sistema Operativo: $os_info"

        if grep -q "Ubuntu 24" /etc/os-release; then
            print_success "Ubuntu 24 LTS detectado"
        elif grep -q "Ubuntu" /etc/os-release; then
            print_warning "No es Ubuntu 24 LTS - puede haber problemas de compatibilidad"
            ((warnings++))
        else
            print_error "Sistema operativo no compatible"
            ((critical_issues++))
        fi
    else
        print_error "No se pudo detectar el sistema operativo"
        ((critical_issues++))
    fi

    # Check architecture
    local arch=$(uname -m)
    echo "🏗️  Arquitectura: $arch"
    if [[ "$arch" == "x86_64" ]]; then
        print_success "Arquitectura x86_64 compatible"
    else
        print_warning "Arquitectura no estándar - puede haber problemas"
        ((warnings++))
    fi

    # Check CPU cores
    local cpu_cores=$(nproc)
    echo "🖥️  CPU Cores: $cpu_cores"
    if [[ $cpu_cores -ge 2 ]]; then
        print_success "Suficientes cores de CPU"
    else
        print_warning "Se recomiendan al menos 2 cores de CPU (funciona con 1 core pero más lento)"
        ((warnings++))
    fi

    # Check memory
    local total_mem=$(free -m | awk 'NR==2{print $2}')
    echo "💾 Memoria Total: ${total_mem}MB"
    if [[ $total_mem -ge 1800 ]]; then
        print_success "Suficiente memoria RAM"
    elif [[ $total_mem -ge 1000 ]]; then
        print_warning "Memoria RAM limitada - puede haber problemas de rendimiento"
        ((warnings++))
    else
        print_error "Memoria RAM insuficiente - se requieren al menos 1GB"
        ((critical_issues++))
    fi

    # Check disk space
    local disk_space=$(df / | awk 'NR==2{print $4}')
    local disk_space_gb=$((disk_space / 1024 / 1024))
    echo "💿 Espacio en Disco: ${disk_space_gb}GB disponibles"
    if [[ $disk_space_gb -ge 15 ]]; then
        print_success "Suficiente espacio en disco"
    elif [[ $disk_space_gb -ge 10 ]]; then
        print_warning "Espacio en disco limitado - se recomienda al menos 20GB"
        ((warnings++))
    else
        print_error "Espacio en disco insuficiente - se requieren al menos 10GB"
        ((critical_issues++))
    fi

    echo ""
    return $critical_issues
}

# Check network connectivity
check_network() {
    print_header "VERIFICACIÓN DE CONECTIVIDAD"

    local critical_issues=0

    # Check internet connectivity
    if curl -s --max-time 10 https://google.com >/dev/null; then
        print_success "Conectividad a internet: OK"
    else
        print_error "Sin conectividad a internet - CRÍTICO"
        ((critical_issues++))
    fi

    # Check GitHub connectivity
    if curl -s --max-time 10 https://github.com >/dev/null; then
        print_success "Conectividad a GitHub: OK"
    else
        print_error "Sin conectividad a GitHub - CRÍTICO"
        ((critical_issues++))
    fi

    # Check Docker Hub connectivity
    if curl -s --max-time 10 https://hub.docker.com >/dev/null; then
        print_success "Conectividad a Docker Hub: OK"
    else
        print_warning "Sin conectividad a Docker Hub - puede afectar la descarga de imágenes"
    fi

    # Check Let's Encrypt connectivity
    if curl -s --max-time 10 https://letsencrypt.org >/dev/null; then
        print_success "Conectividad a Let's Encrypt: OK"
    else
        print_warning "Sin conectividad a Let's Encrypt - usar certificados auto-firmados"
    fi

    echo ""
    return $critical_issues
}

# Check user permissions
check_permissions() {
    print_header "VERIFICACIÓN DE PERMISOS"
    
    local issues=0
    
    # Check if running as root
    if [[ $EUID -eq 0 ]]; then
        print_error "No ejecutes este script como root"
        print_error "Usa un usuario con permisos sudo"
        ((issues++))
    else
        print_success "Ejecutándose como usuario no-root"
    fi
    
    # Check sudo access
    if sudo -n true 2>/dev/null; then
        print_success "Acceso sudo sin contraseña: OK"
    elif sudo -v 2>/dev/null; then
        print_success "Acceso sudo: OK"
    else
        print_error "Sin acceso sudo - requerido para la instalación"
        ((issues++))
    fi
    
    # Check write permissions in /opt
    if sudo touch /opt/test-write 2>/dev/null && sudo rm /opt/test-write 2>/dev/null; then
        print_success "Permisos de escritura en /opt: OK"
    else
        print_error "Sin permisos de escritura en /opt"
        ((issues++))
    fi
    
    echo ""
    return $issues
}

# Check ports availability
check_ports() {
    print_header "VERIFICACIÓN DE PUERTOS"

    local critical_issues=0
    local required_ports=(80 443 3000 5432)

    for port in "${required_ports[@]}"; do
        if netstat -tuln 2>/dev/null | grep -q ":$port "; then
            print_warning "Puerto $port ya está en uso - se manejará automáticamente"
        else
            print_success "Puerto $port disponible"
        fi
    done

    echo ""
    return $critical_issues
}

# Check existing installations
check_existing_installations() {
    print_header "VERIFICACIÓN DE INSTALACIONES EXISTENTES"
    
    local warnings=0
    
    # Check if Docker is already installed
    if command -v docker >/dev/null 2>&1; then
        local docker_version=$(docker --version | cut -d' ' -f3 | tr -d ',')
        print_warning "Docker ya está instalado: $docker_version"
        ((warnings++))
    else
        print_success "Docker no instalado - se instalará"
    fi
    
    # Check if Nginx is already installed
    if command -v nginx >/dev/null 2>&1; then
        local nginx_version=$(nginx -v 2>&1 | cut -d' ' -f3)
        print_warning "Nginx ya está instalado: $nginx_version"
        ((warnings++))
    else
        print_success "Nginx no instalado - se instalará"
    fi
    
    # Check if Node.js is already installed
    if command -v node >/dev/null 2>&1; then
        local node_version=$(node --version)
        print_warning "Node.js ya está instalado: $node_version"
        ((warnings++))
    else
        print_success "Node.js no instalado - se instalará"
    fi
    
    # Check if project directory exists
    if [[ -d "/opt/tuchanga" ]]; then
        print_warning "Directorio /opt/tuchanga ya existe"
        print_warning "El contenido existente será respaldado"
        ((warnings++))
    else
        print_success "Directorio del proyecto no existe - se creará"
    fi
    
    if [[ $warnings -gt 0 ]]; then
        echo ""
        print_warning "Se encontraron $warnings instalaciones existentes"
        print_warning "Los scripts manejarán estas situaciones automáticamente"
    fi
    
    echo ""
    return 0
}

# Check DNS configuration (if domain provided)
check_dns() {
    local domain="$1"

    if [[ -z "$domain" ]]; then
        return 0
    fi

    print_header "VERIFICACIÓN DE DNS PARA $domain"

    local critical_issues=0

    # Get server public IP
    local server_ip=$(curl -s --max-time 10 http://***************/latest/meta-data/public-ipv4 2>/dev/null || curl -s --max-time 10 https://ipinfo.io/ip)

    if [[ -n "$server_ip" ]]; then
        echo "🌐 IP del servidor: $server_ip"

        # Check domain resolution
        local domain_ip=$(dig +short "$domain" | tail -n1)
        if [[ -n "$domain_ip" ]]; then
            echo "🔍 $domain resuelve a: $domain_ip"

            if [[ "$server_ip" == "$domain_ip" ]]; then
                print_success "DNS configurado correctamente para $domain"
            else
                print_warning "DNS no apunta a este servidor - se puede configurar después"
                print_warning "Configura el registro A: $domain → $server_ip"
            fi
        else
            print_warning "El dominio $domain no resuelve - se puede configurar después"
        fi

        # Check www subdomain
        local www_ip=$(dig +short "www.$domain" | tail -n1)
        if [[ -n "$www_ip" ]]; then
            if [[ "$server_ip" == "$www_ip" ]]; then
                print_success "DNS configurado correctamente para www.$domain"
            else
                print_warning "www.$domain no apunta a este servidor"
            fi
        else
            print_warning "www.$domain no está configurado"
        fi

        # Check api subdomain
        local api_ip=$(dig +short "api.$domain" | tail -n1)
        if [[ -n "$api_ip" ]]; then
            if [[ "$server_ip" == "$api_ip" ]]; then
                print_success "DNS configurado correctamente para api.$domain"
            else
                print_warning "api.$domain no apunta a este servidor"
            fi
        else
            print_warning "api.$domain no está configurado"
        fi

    else
        print_warning "No se pudo obtener la IP pública del servidor"
    fi

    echo ""
    return $critical_issues
}

# Show summary and recommendations
show_summary() {
    local critical_issues="$1"

    print_header "RESUMEN DE VERIFICACIÓN"

    if [[ $critical_issues -eq 0 ]]; then
        print_success "✅ No hay problemas críticos"
        print_success "🚀 El sistema está listo para la instalación de Tuchanga"
        echo ""
        print_status "Los warnings mostrados no impiden la instalación"
        print_status "Los scripts manejarán automáticamente las situaciones detectadas"
        echo ""
    else
        print_error "❌ Se encontraron $critical_issues problemas críticos"
        echo ""
        print_status "Problemas críticos deben resolverse antes de continuar:"

        if [[ $critical_issues -gt 3 ]]; then
            print_error "Demasiados problemas críticos - no se recomienda continuar"
            echo ""
            print_status "Resuelve los problemas críticos y vuelve a ejecutar la verificación"
        else
            print_warning "Algunos problemas críticos pueden resolverse automáticamente"
            print_warning "Revisa los errores antes de continuar"
            echo ""
            print_status "Para intentar la instalación de todos modos:"
            echo "  Continúa con la instalación bajo tu responsabilidad"
        fi
        echo ""
    fi
}

# Main function
main() {
    clear
    print_header "TUCHANGA - VERIFICACIÓN PRE-INSTALACIÓN"
    echo "Verificando requisitos del sistema para Ubuntu 24 LTS..."
    echo ""

    local critical_issues=0

    # Run all checks - only count critical issues
    check_system_requirements
    critical_issues=$((critical_issues + $?))

    check_network
    critical_issues=$((critical_issues + $?))

    check_permissions
    critical_issues=$((critical_issues + $?))

    check_ports
    critical_issues=$((critical_issues + $?))

    check_existing_installations
    # Don't count warnings as critical issues

    # Check DNS if domain provided
    if [[ -n "$1" ]]; then
        check_dns "$1"
        critical_issues=$((critical_issues + $?))
    else
        print_status "Para verificar DNS, ejecuta: $0 tu-dominio.com"
        echo ""
    fi

    show_summary $critical_issues

    # Only exit with error code if there are critical issues
    if [[ $critical_issues -gt 0 ]]; then
        print_warning "Verificación completada con $critical_issues problemas críticos"
        exit $critical_issues
    else
        print_success "Verificación completada exitosamente"
        exit 0
    fi
}

# Run main function
main "$@"
