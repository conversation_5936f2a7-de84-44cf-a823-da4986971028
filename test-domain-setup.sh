#!/bin/bash

# Test custom domain setup for Tuchanga Job Platform
# This script verifies that your custom domain is working correctly

set -e

echo "🧪 Testing custom domain setup..."

# Get domain from user
read -p "Enter your domain name to test (e.g., midominio.com): " DOMAIN

if [[ -z "$DOMAIN" ]]; then
    echo "❌ Domain name is required"
    exit 1
fi

echo ""
echo "🔍 Testing domain: $DOMAIN"
echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"

# Test DNS resolution
echo "📡 Testing DNS resolution..."

# Test main domain
if nslookup $DOMAIN > /dev/null 2>&1; then
    MAIN_IP=$(nslookup $DOMAIN | grep -A1 "Name:" | tail -1 | awk '{print $2}' || echo "N/A")
    echo "✅ $DOMAIN resolves to: $MAIN_IP"
else
    echo "❌ $DOMAIN does not resolve"
fi

# Test www subdomain
if nslookup www.$DOMAIN > /dev/null 2>&1; then
    WWW_IP=$(nslookup www.$DOMAIN | grep -A1 "Name:" | tail -1 | awk '{print $2}' || echo "N/A")
    echo "✅ www.$DOMAIN resolves to: $WWW_IP"
else
    echo "❌ www.$DOMAIN does not resolve"
fi

# Test api subdomain
if nslookup api.$DOMAIN > /dev/null 2>&1; then
    API_IP=$(nslookup api.$DOMAIN | grep -A1 "Name:" | tail -1 | awk '{print $2}' || echo "N/A")
    echo "✅ api.$DOMAIN resolves to: $API_IP"
else
    echo "❌ api.$DOMAIN does not resolve"
fi

echo ""
echo "🌐 Testing HTTP/HTTPS connectivity..."

# Test main domain HTTPS
echo -n "Testing https://$DOMAIN ... "
if curl -s -I --max-time 10 https://$DOMAIN > /dev/null 2>&1; then
    STATUS=$(curl -s -I --max-time 10 https://$DOMAIN | head -1 | awk '{print $2}')
    echo "✅ Status: $STATUS"
else
    echo "❌ Failed"
fi

# Test www domain HTTPS
echo -n "Testing https://www.$DOMAIN ... "
if curl -s -I --max-time 10 https://www.$DOMAIN > /dev/null 2>&1; then
    STATUS=$(curl -s -I --max-time 10 https://www.$DOMAIN | head -1 | awk '{print $2}')
    echo "✅ Status: $STATUS"
else
    echo "❌ Failed"
fi

# Test API domain HTTPS
echo -n "Testing https://api.$DOMAIN ... "
if curl -s -I --max-time 10 https://api.$DOMAIN > /dev/null 2>&1; then
    STATUS=$(curl -s -I --max-time 10 https://api.$DOMAIN | head -1 | awk '{print $2}')
    echo "✅ Status: $STATUS"
else
    echo "❌ Failed"
fi

echo ""
echo "🔧 Testing API functionality..."

# Test API health endpoint
echo -n "Testing API health endpoint ... "
if curl -s --max-time 10 https://api.$DOMAIN/health > /dev/null 2>&1; then
    echo "✅ API health check passed"
else
    echo "❌ API health check failed"
fi

# Test API with sample request
echo -n "Testing API sample request ... "
if curl -s --max-time 10 https://api.$DOMAIN/api/jobs > /dev/null 2>&1; then
    echo "✅ API sample request successful"
else
    echo "❌ API sample request failed"
fi

echo ""
echo "🔐 Testing SSL certificates..."

# Check SSL certificate
echo -n "Checking SSL certificate for $DOMAIN ... "
if openssl s_client -connect $DOMAIN:443 -servername $DOMAIN < /dev/null 2>/dev/null | openssl x509 -noout -dates > /dev/null 2>&1; then
    EXPIRY=$(openssl s_client -connect $DOMAIN:443 -servername $DOMAIN < /dev/null 2>/dev/null | openssl x509 -noout -dates | grep "notAfter" | cut -d= -f2)
    echo "✅ Valid until: $EXPIRY"
else
    echo "❌ SSL certificate check failed"
fi

echo ""
echo "📊 Container status:"
docker-compose ps

echo ""
echo "📋 Summary:"
echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"

# Get current server IP
if curl -s --max-time 3 http://***************/latest/meta-data/public-ipv4 &>/dev/null; then
    SERVER_IP=$(curl -s http://***************/latest/meta-data/public-ipv4)
    echo "🖥️  Server IP: $SERVER_IP"
else
    echo "🖥️  Server IP: Unable to determine"
fi

echo "🌐 Domains:"
echo "   Main:  https://$DOMAIN"
echo "   WWW:   https://www.$DOMAIN"
echo "   API:   https://api.$DOMAIN"

echo ""
echo "💡 Troubleshooting tips:"
echo "   • If DNS resolution fails, check your domain's DNS settings"
echo "   • If HTTPS fails, check SSL certificates: docker-compose logs frontend"
echo "   • If API fails, check backend logs: docker-compose logs backend"
echo "   • DNS changes can take 5-30 minutes to propagate"

echo ""
echo "🔧 Useful commands:"
echo "   docker-compose ps              # Check container status"
echo "   docker-compose logs frontend   # Check frontend/nginx logs"
echo "   docker-compose logs backend    # Check backend logs"
echo "   docker-compose restart         # Restart all services"

echo ""
echo "✅ Domain test complete!"
