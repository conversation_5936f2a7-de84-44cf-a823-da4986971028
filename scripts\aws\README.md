# TuChanga AWS Deployment Scripts

Este directorio contiene todos los scripts necesarios para desplegar y gestionar TuChanga en AWS usando ECS + Aurora RDS.

## 🚀 Inicio Rápido

```bash
# Navegar al directorio de scripts
cd scripts/aws

# Ejecutar el script principal interactivo
./main.sh
```

## 📁 Estructura de Archivos

```
scripts/aws/
├── main.sh                      # 🎯 Script principal interactivo
├── 01-full-deploy.sh           # 🏗️  Deploy completo desde 0
├── 02-deploy-containers.sh     # 🐳 Deploy solo contenedores
├── 03-backup-database.sh       # 💾 Backup de base de datos
├── 04-update-deployment.sh     # 🔄 Actualizar deployment
├── AWS_PERMISSIONS_REQUIRED.md # 📋 Permisos AWS necesarios
└── README.md                   # 📖 Esta documentación
```

## 🎯 Script Principal: `main.sh`

El script principal es **interactivo** y te permite elegir qué operación realizar:

### Funcionalidades Disponibles:

#### 📦 **DEPLOYMENT**
1. **Full deployment desde 0** - Crea toda la infraestructura AWS
2. **Deploy contenedores** - Solo actualiza contenedores
3. **Update deployment** - Actualiza backend/frontend/ambos

#### 🗄️ **DATABASE**
4. **Backup de base de datos** - Crea backup y envía por email
5. **Check database status** - Verifica estado de Aurora

#### 🔧 **MANAGEMENT**
6. **View deployment status** - Estado actual del deployment
7. **View CloudWatch logs** - Logs de backend/frontend
8. **Clean up resources** - Elimina todos los recursos (PELIGROSO)

#### 📚 **HELP**
9. **AWS permissions** - Muestra permisos necesarios
10. **Troubleshooting** - Guía de resolución de problemas

## 📋 Scripts Individuales

### 1. `01-full-deploy.sh` - Deploy Completo

Crea toda la infraestructura desde cero:

- ✅ VPC con subnets públicas y privadas
- ✅ Security Groups
- ✅ Aurora PostgreSQL cluster
- ✅ ECS Cluster
- ✅ ECR repositories
- ✅ IAM roles
- ✅ Application Load Balancer
- ✅ Secrets Manager
- ✅ CloudWatch Log Groups

```bash
./01-full-deploy.sh
```

**Tiempo estimado:** 15-20 minutos

### 2. `02-deploy-containers.sh` - Deploy Contenedores

Construye y despliega solo los contenedores:

- 🏗️ Build backend image
- 🏗️ Build frontend image
- 📤 Push a ECR
- 🚀 Deploy a ECS

```bash
./02-deploy-containers.sh
```

**Tiempo estimado:** 5-10 minutos

### 3. `03-backup-database.sh` - Backup Base de Datos

Crea backup completo de la base de datos:

- 📸 Aurora snapshot
- 💾 Logical backup (pg_dump)
- ☁️ Upload a S3
- 📧 Envío por email

```bash
# Con email como argumento
./03-backup-database.sh <EMAIL>

# Modo interactivo (pedirá email)
./03-backup-database.sh
```

### 4. `04-update-deployment.sh` - Actualizar Deployment

Actualiza componentes específicos:

```bash
# Actualizar solo backend (incluye migraciones DB)
./04-update-deployment.sh backend

# Actualizar solo frontend
./04-update-deployment.sh frontend

# Actualizar ambos
./04-update-deployment.sh both

# Modo interactivo
./04-update-deployment.sh
```

## ⚙️ Configuración Previa

### 1. Permisos AWS

Revisa los permisos necesarios:
```bash
cat AWS_PERMISSIONS_REQUIRED.md
```

### 2. Configurar AWS CLI

```bash
aws configure
# Ingresa: Access Key, Secret Key, Region (sa-east-1), Output format (json)
```

### 3. Verificar configuración

```bash
aws sts get-caller-identity
```

## 🏗️ Arquitectura Desplegada

```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   CloudFront    │    │  Application     │    │   Aurora RDS    │
│   (Opcional)    │────│  Load Balancer   │    │   PostgreSQL    │
└─────────────────┘    │   (ALB + SSL)    │    │   (Managed)     │
                       └──────────────────┘    └─────────────────┘
                              │                          │
                    ┌─────────┼─────────┐               │
                    │                   │               │
            ┌───────▼────────┐  ┌──────▼────────┐      │
            │  ECS Service   │  │  ECS Service  │      │
            │   Frontend     │  │   Backend     │──────┘
            │  (Nginx)       │  │  (NestJS)     │
            └────────────────┘  └───────────────┘
```

## 🔧 Variables de Configuración

Los scripts usan estas variables por defecto:

```bash
AWS_REGION="sa-east-1"
PROJECT_NAME="tuchanga"
VPC_CIDR="10.0.0.0/16"
```

Para cambiar la región, edita la variable `AWS_REGION` en cada script.

## 📊 Monitoreo y Logs

### CloudWatch Logs
- Backend: `/ecs/tuchanga-backend`
- Frontend: `/ecs/tuchanga-frontend`

### Métricas
- ECS Service metrics
- ALB metrics
- Aurora metrics

## 🆘 Troubleshooting

### Problemas Comunes

1. **Error de permisos**
   ```bash
   # Verificar permisos
   aws sts get-caller-identity
   ```

2. **Docker no funciona**
   ```bash
   # Verificar Docker
   docker ps
   sudo systemctl start docker  # Linux
   ```

3. **ECR login falla**
   ```bash
   # Re-login a ECR
   aws ecr get-login-password --region sa-east-1 | docker login --username AWS --password-stdin ACCOUNT_ID.dkr.ecr.sa-east-1.amazonaws.com
   ```

4. **Base de datos no conecta**
   - Verificar security groups
   - Verificar Aurora cluster status
   - Verificar secrets en Secrets Manager

### Logs Útiles

```bash
# Ver logs de backend
aws logs tail "/ecs/tuchanga-backend" --follow

# Ver logs de frontend
aws logs tail "/ecs/tuchanga-frontend" --follow

# Ver estado de servicios ECS
aws ecs describe-services --cluster tuchanga-cluster --services tuchanga-backend-service
```

## 🔄 Flujo de Trabajo Típico

### Primera vez (Deploy completo):
1. `./main.sh` → Opción 1 (Full deployment)
2. Esperar 15-20 minutos
3. Verificar en AWS Console

### Actualizaciones de código:
1. `./main.sh` → Opción 3 (Update deployment)
2. Elegir backend/frontend/both
3. Esperar 5-10 minutos

### Backup regular:
1. `./main.sh` → Opción 4 (Database backup)
2. Ingresar email
3. Recibir backup por email

## 💰 Costos Estimados (AWS sa-east-1)

- **Aurora PostgreSQL (db.t3.medium):** ~$50/mes
- **ECS Fargate (2 tasks, 0.25 vCPU, 0.5GB):** ~$15/mes
- **Application Load Balancer:** ~$20/mes
- **Data Transfer:** Variable
- **Total estimado:** ~$85-100/mes

## 🔒 Seguridad

- Todas las contraseñas se almacenan en AWS Secrets Manager
- ECS tasks corren en subnets privadas
- Solo ALB tiene acceso público
- Security groups configurados con principio de menor privilegio

## 📞 Soporte

Si tienes problemas:

1. Ejecuta `./main.sh` → Opción 10 (Troubleshooting)
2. Revisa CloudWatch logs
3. Verifica permisos AWS
4. Consulta la documentación de AWS

---

**¡Listo para desplegar TuChanga en AWS! 🚀**
