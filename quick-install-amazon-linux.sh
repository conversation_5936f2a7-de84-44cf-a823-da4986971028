#!/bin/bash

# Quick installation script for Amazon Linux EC2
# One-command setup for Tuchanga Job Platform

set -e

echo "⚡ Quick Install - Tuchanga Job Platform on Amazon Linux"
echo "======================================================="

# Check if running as root
if [[ $EUID -eq 0 ]]; then
   echo "❌ This script should not be run as root"
   echo "   Run as ec2-user: su - ec2-user"
   exit 1
fi

# Check if running on Amazon Linux
if [[ -f /etc/os-release ]]; then
    source /etc/os-release
    if [[ "$ID" != "amzn" && "$ID" != "amazon" ]]; then
        echo "⚠️  This script is optimized for Amazon Linux"
        echo "Current OS: $PRETTY_NAME"
        read -p "Continue anyway? (y/n): " confirm
        if [[ $confirm != "y" ]]; then
            exit 1
        fi
    fi
fi

echo "🔄 Step 1: System Update and Dependencies"
echo "========================================="

# Update system
sudo yum update -y

# Install essential packages
sudo yum install -y \
    git \
    curl \
    wget \
    openssl \
    python3 \
    python3-pip \
    htop

echo "✅ System updated"

echo ""
echo "🐳 Step 2: Docker Installation"
echo "=============================="

# Install Docker
if ! command -v docker &> /dev/null; then
    sudo yum install -y docker
    sudo systemctl start docker
    sudo systemctl enable docker
    sudo usermod -a -G docker ec2-user
    echo "✅ Docker installed"
else
    echo "✅ Docker already installed"
    sudo systemctl start docker
fi

# Install Docker Compose
if ! command -v docker-compose &> /dev/null; then
    sudo curl -L "https://github.com/docker/compose/releases/latest/download/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
    sudo chmod +x /usr/local/bin/docker-compose
    sudo ln -sf /usr/local/bin/docker-compose /usr/bin/docker-compose
    echo "✅ Docker Compose installed"
else
    echo "✅ Docker Compose already installed"
fi

echo ""
echo "🔐 Step 3: SSL Certificate Generation"
echo "===================================="

# Create SSL directory
mkdir -p ssl

# Generate self-signed certificate
DOMAIN="ec2-18-231-110-178.sa-east-1.compute.amazonaws.com"

echo "🔑 Generating SSL certificates for $DOMAIN..."
openssl genrsa -out ssl/key.pem 2048
openssl req -new -x509 -key ssl/key.pem -out ssl/cert.pem -days 365 \
    -subj "/C=AR/ST=Cordoba/L=Cordoba/O=Tuchanga/CN=$DOMAIN"

# Set permissions
chmod 644 ssl/cert.pem
chmod 600 ssl/key.pem

echo "✅ SSL certificates generated"

echo ""
echo "🚀 Step 4: Application Deployment"
echo "================================="

# Check if project files exist
if [[ ! -f "docker-compose.yml" ]]; then
    echo "❌ docker-compose.yml not found"
    echo "Make sure you're in the project directory"
    exit 1
fi

# Stop any existing containers
docker-compose down || true

# Build and start containers
echo "🔨 Building and starting containers..."
docker-compose up -d --build

# Wait for containers
echo "⏳ Waiting for containers to start..."
sleep 15

echo ""
echo "📊 Step 5: Verification"
echo "======================"

# Check container status
echo "Container status:"
docker-compose ps

# Test local connections
echo ""
echo "🧪 Testing connections..."

if curl -k -s https://localhost:443 &>/dev/null; then
    echo "✅ HTTPS (443) - Local connection successful"
else
    echo "⚠️  HTTPS (443) - Local connection failed"
fi

if curl -s http://localhost:3000/health &>/dev/null; then
    echo "✅ Backend (3000) - Health check successful"
else
    echo "⚠️  Backend (3000) - Health check failed"
fi

# Get public IP and DNS
if curl -s --max-time 3 http://***************/latest/meta-data/public-ipv4 &>/dev/null; then
    PUBLIC_IP=$(curl -s http://***************/latest/meta-data/public-ipv4)
    PUBLIC_DNS=$(curl -s http://***************/latest/meta-data/public-hostname)
    
    echo ""
    echo "🌐 Your application URLs:"
    echo "   HTTPS: https://$PUBLIC_DNS"
    echo "   HTTP:  http://$PUBLIC_DNS (redirects to HTTPS)"
    echo "   API:   http://$PUBLIC_IP:3000"
else
    echo "⚠️  Could not determine public IP/DNS"
fi

echo ""
echo "🎉 Installation Complete!"
echo "========================"

echo ""
echo "📋 Important Notes:"
echo "   ⚠️  Self-signed certificates will show browser warnings"
echo "   ⚠️  Click 'Advanced' → 'Proceed to site' to continue"
echo "   ⚠️  Make sure AWS Security Groups allow ports 80, 443, and 3000"
echo ""

echo "🔧 Useful Commands:"
echo "   docker-compose ps              # Check container status"
echo "   docker-compose logs            # View all logs"
echo "   docker-compose logs frontend   # View frontend logs"
echo "   docker-compose logs backend    # View backend logs"
echo "   docker-compose restart         # Restart all containers"
echo "   docker-compose down            # Stop all containers"
echo ""

echo "🔍 Security Group Check:"
echo "   Run: ./check-aws-setup.sh"
echo ""

echo "🔄 For Let's Encrypt certificates:"
echo "   Run: ./setup-https.sh (choose option 2)"
echo ""

# Check if user needs to re-login for docker group
if ! groups | grep -q docker; then
    echo "⚠️  IMPORTANT: You need to log out and back in for Docker group changes to take effect"
    echo "   After re-login, you can run Docker commands without sudo"
    echo ""
fi

echo "✅ Quick install complete!"
echo ""
echo "🌐 Visit your application at: https://$DOMAIN"
