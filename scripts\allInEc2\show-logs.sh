#!/bin/bash

# Show logs for Tuchanga services
# This script displays logs from various services

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m'

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_header() {
    echo -e "${PURPLE}================================${NC}"
    echo -e "${PURPLE}$1${NC}"
    echo -e "${PURPLE}================================${NC}"
}

# Configuration
PROJECT_DIR="/opt/tuchanga"
LINES="${2:-50}"  # Default to 50 lines

# Function to check if command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Show Docker container logs
show_container_logs() {
    local container="$1"
    local lines="$2"
    
    if docker ps --format "{{.Names}}" | grep -q "^$container$"; then
        print_header "LOGS: $container (últimas $lines líneas)"
        docker logs "$container" --tail "$lines" --timestamps
        echo ""
    elif docker ps -a --format "{{.Names}}" | grep -q "^$container$"; then
        print_header "LOGS: $container (contenedor detenido - últimas $lines líneas)"
        docker logs "$container" --tail "$lines" --timestamps
        echo ""
    else
        print_warning "Contenedor $container no encontrado"
        echo ""
    fi
}

# Show Nginx logs
show_nginx_logs() {
    local lines="$1"
    
    print_header "NGINX ACCESS LOG (últimas $lines líneas)"
    if [[ -f "/var/log/nginx/access.log" ]]; then
        tail -n "$lines" /var/log/nginx/access.log
    else
        print_warning "Archivo de log de acceso de Nginx no encontrado"
    fi
    echo ""
    
    print_header "NGINX ERROR LOG (últimas $lines líneas)"
    if [[ -f "/var/log/nginx/error.log" ]]; then
        tail -n "$lines" /var/log/nginx/error.log
    else
        print_warning "Archivo de log de errores de Nginx no encontrado"
    fi
    echo ""
}

# Show system logs
show_system_logs() {
    local lines="$1"
    
    print_header "SYSTEM LOG - DOCKER (últimas $lines líneas)"
    journalctl -u docker --no-pager -n "$lines" --output=short-iso
    echo ""
    
    print_header "SYSTEM LOG - NGINX (últimas $lines líneas)"
    journalctl -u nginx --no-pager -n "$lines" --output=short-iso
    echo ""
}

# Show application logs
show_application_logs() {
    local lines="$1"
    
    print_header "LOGS DE APLICACIÓN TUCHANGA"
    
    # Backend logs
    show_container_logs "tuchanga-backend" "$lines"
    
    # Database logs
    show_container_logs "tuchanga-postgres" "$lines"
    
    # Frontend container logs (if exists)
    show_container_logs "tuchanga-frontend" "$lines"
}

# Show error logs only
show_error_logs() {
    local lines="$1"
    
    print_header "ERRORES RECIENTES EN TODOS LOS SERVICIOS"
    
    # Docker container errors
    local containers=("tuchanga-backend" "tuchanga-postgres" "tuchanga-frontend")
    
    for container in "${containers[@]}"; do
        if docker ps -a --format "{{.Names}}" | grep -q "^$container$"; then
            local errors=$(docker logs "$container" --tail 100 2>&1 | grep -i "error\|exception\|failed\|fatal" | tail -n "$lines")
            if [[ -n "$errors" ]]; then
                echo -e "${RED}=== ERRORES EN $container ===${NC}"
                echo "$errors"
                echo ""
            fi
        fi
    done
    
    # Nginx errors
    if [[ -f "/var/log/nginx/error.log" ]]; then
        local nginx_errors=$(grep -i "error\|crit\|alert\|emerg" /var/log/nginx/error.log | tail -n "$lines")
        if [[ -n "$nginx_errors" ]]; then
            echo -e "${RED}=== ERRORES EN NGINX ===${NC}"
            echo "$nginx_errors"
            echo ""
        fi
    fi
    
    # System errors
    local system_errors=$(journalctl --no-pager -p err -n "$lines" --output=short-iso)
    if [[ -n "$system_errors" ]]; then
        echo -e "${RED}=== ERRORES DEL SISTEMA ===${NC}"
        echo "$system_errors"
        echo ""
    fi
}

# Follow logs in real time
follow_logs() {
    local service="$1"
    
    case "$service" in
        "backend")
            print_status "Siguiendo logs del backend en tiempo real (Ctrl+C para salir)..."
            docker logs -f tuchanga-backend
            ;;
        "database"|"db")
            print_status "Siguiendo logs de la base de datos en tiempo real (Ctrl+C para salir)..."
            docker logs -f tuchanga-postgres
            ;;
        "nginx")
            print_status "Siguiendo logs de Nginx en tiempo real (Ctrl+C para salir)..."
            tail -f /var/log/nginx/access.log /var/log/nginx/error.log
            ;;
        "all")
            print_status "Siguiendo logs de todos los contenedores en tiempo real (Ctrl+C para salir)..."
            cd "$PROJECT_DIR"
            docker-compose logs -f
            ;;
        *)
            print_error "Servicio desconocido para seguimiento: $service"
            echo "Servicios disponibles: backend, database, nginx, all"
            exit 1
            ;;
    esac
}

# Show logs menu
show_logs_menu() {
    echo ""
    print_status "Selecciona qué logs ver:"
    echo "1. 📋 Logs de aplicación (todos los contenedores)"
    echo "2. ⚙️  Logs del backend"
    echo "3. 🗄️  Logs de la base de datos"
    echo "4. 🌐 Logs de Nginx"
    echo "5. 🖥️  Logs del sistema"
    echo "6. ❌ Solo errores"
    echo "7. 👁️  Seguir logs en tiempo real"
    echo "0. ❌ Salir"
    echo ""
}

# Show real-time menu
show_realtime_menu() {
    echo ""
    print_status "Selecciona qué logs seguir en tiempo real:"
    echo "1. ⚙️  Backend"
    echo "2. 🗄️  Base de datos"
    echo "3. 🌐 Nginx"
    echo "4. 📋 Todos los contenedores"
    echo "0. ⬅️  Volver al menú principal"
    echo ""
}

# Export logs to file
export_logs() {
    local export_dir="/tmp/tuchanga-logs-$(date +%Y%m%d-%H%M%S)"
    mkdir -p "$export_dir"
    
    print_status "Exportando logs a $export_dir..."
    
    # Export container logs
    local containers=("tuchanga-backend" "tuchanga-postgres" "tuchanga-frontend")
    
    for container in "${containers[@]}"; do
        if docker ps -a --format "{{.Names}}" | grep -q "^$container$"; then
            docker logs "$container" --timestamps > "$export_dir/$container.log" 2>&1
        fi
    done
    
    # Export Nginx logs
    if [[ -f "/var/log/nginx/access.log" ]]; then
        cp /var/log/nginx/access.log "$export_dir/nginx-access.log"
    fi
    
    if [[ -f "/var/log/nginx/error.log" ]]; then
        cp /var/log/nginx/error.log "$export_dir/nginx-error.log"
    fi
    
    # Export system logs
    journalctl -u docker --no-pager --output=short-iso > "$export_dir/system-docker.log"
    journalctl -u nginx --no-pager --output=short-iso > "$export_dir/system-nginx.log"
    
    # Create summary
    cat > "$export_dir/README.txt" << EOF
Tuchanga Logs Export
Fecha: $(date)
Servidor: $(hostname)

Archivos incluidos:
- tuchanga-backend.log: Logs del backend API
- tuchanga-postgres.log: Logs de la base de datos
- tuchanga-frontend.log: Logs del frontend (si existe)
- nginx-access.log: Logs de acceso de Nginx
- nginx-error.log: Logs de errores de Nginx
- system-docker.log: Logs del sistema para Docker
- system-nginx.log: Logs del sistema para Nginx

Para comprimir todos los logs:
tar -czf tuchanga-logs.tar.gz $(basename "$export_dir")
EOF
    
    print_success "Logs exportados a: $export_dir"
    echo ""
    print_status "Para comprimir los logs:"
    echo "tar -czf tuchanga-logs.tar.gz $(basename "$export_dir")"
    echo ""
}

# Main function
main() {
    print_status "Visualizador de logs de Tuchanga"
    echo ""
    
    # Check prerequisites
    if ! command_exists docker; then
        print_error "Docker no está instalado"
        exit 1
    fi
    
    # Handle command line arguments
    if [[ $# -gt 0 ]]; then
        case "$1" in
            "backend")
                show_container_logs "tuchanga-backend" "$LINES"
                ;;
            "database"|"db")
                show_container_logs "tuchanga-postgres" "$LINES"
                ;;
            "frontend")
                show_container_logs "tuchanga-frontend" "$LINES"
                ;;
            "nginx")
                show_nginx_logs "$LINES"
                ;;
            "system")
                show_system_logs "$LINES"
                ;;
            "errors")
                show_error_logs "$LINES"
                ;;
            "all")
                show_application_logs "$LINES"
                ;;
            "follow")
                if [[ -n "$2" ]]; then
                    follow_logs "$2"
                else
                    print_error "Especifica el servicio a seguir: backend, database, nginx, all"
                    exit 1
                fi
                ;;
            "export")
                export_logs
                ;;
            *)
                print_error "Argumento inválido: $1"
                echo "Uso: $0 [backend|database|nginx|system|errors|all|follow <service>|export] [lines]"
                exit 1
                ;;
        esac
        return 0
    fi
    
    # Interactive menu
    while true; do
        show_logs_menu
        read -p "Ingresa tu opción [0-7]: " choice
        
        case $choice in
            1)
                show_application_logs "$LINES"
                read -p "Presiona Enter para continuar..."
                ;;
            2)
                show_container_logs "tuchanga-backend" "$LINES"
                read -p "Presiona Enter para continuar..."
                ;;
            3)
                show_container_logs "tuchanga-postgres" "$LINES"
                read -p "Presiona Enter para continuar..."
                ;;
            4)
                show_nginx_logs "$LINES"
                read -p "Presiona Enter para continuar..."
                ;;
            5)
                show_system_logs "$LINES"
                read -p "Presiona Enter para continuar..."
                ;;
            6)
                show_error_logs "$LINES"
                read -p "Presiona Enter para continuar..."
                ;;
            7)
                show_realtime_menu
                read -p "Ingresa tu opción [0-4]: " rt_choice
                case $rt_choice in
                    1) follow_logs "backend" ;;
                    2) follow_logs "database" ;;
                    3) follow_logs "nginx" ;;
                    4) follow_logs "all" ;;
                    0) continue ;;
                    *) print_error "Opción inválida" ;;
                esac
                ;;
            0)
                print_success "¡Hasta luego!"
                exit 0
                ;;
            *)
                print_error "Opción inválida. Intenta de nuevo."
                ;;
        esac
    done
}

# Run main function
main "$@"
