# Configuración de Dominio Personalizado para Tuchanga

Esta guía te ayudará a configurar tu dominio personalizado para que apunte a tu aplicación Tuchanga deployada en AWS EC2.

## 🎯 Objetivo

Configurar tu dominio para que:
- `midominio.com` → Frontend de la aplicación
- `www.midominio.com` → Frontend de la aplicación  
- `api.midominio.com` → Backend API (puerto 3000)

## 📋 Requisitos Previos

1. ✅ Dominio comprado en un hosting
2. ✅ Aplicación Tuchanga deployada en EC2
3. ✅ Acceso al panel de control DNS de tu hosting

## 🚀 Configuración Automática

### Paso 1: Ejecutar el script de configuración

```bash
# Hacer ejecutable el script
chmod +x setup-custom-domain.sh

# Ejecutar configuración
./setup-custom-domain.sh
```

El script te pedirá:
- Tu nombre de dominio (ej: `midominio.com`)
- Tipo de certificado SSL (recomendado: Let's Encrypt)
- Email para notificaciones SSL

### Paso 2: Configurar DNS en tu hosting

Agrega estos registros A en el panel DNS de tu hosting:

| Nombre | Tipo | Valor | TTL |
|--------|------|-------|-----|
| @ | A | ************** | 300 |
| www | A | ************** | 300 |
| api | A | ************** | 300 |

### Paso 3: Verificar configuración

```bash
# Verificar que todo funciona
./test-domain-setup.sh
```

## 🔧 Configuración Manual

### 1. Configuración DNS

#### Opción A: Registros A (Recomendado)
```
Tipo: A
Nombre: @
Valor: **************
TTL: 300

Tipo: A
Nombre: www
Valor: **************
TTL: 300

Tipo: A
Nombre: api
Valor: **************
TTL: 300
```

#### Opción B: Registro CNAME (Alternativo)
```
Tipo: CNAME
Nombre: www
Valor: midominio.com
TTL: 300

Tipo: CNAME
Nombre: api
Valor: midominio.com
TTL: 300
```

### 2. Actualizar configuración Nginx

Edita `frontend/nginx.conf` y reemplaza `midominio.com` con tu dominio real.

### 3. Generar certificados SSL

#### Let's Encrypt (Recomendado)
```bash
# Instalar certbot (Amazon Linux)
sudo yum install -y python3-pip
sudo pip3 install certbot

# Obtener certificado
sudo certbot certonly --standalone \
    --email <EMAIL> \
    --agree-tos \
    -d midominio.com \
    -d www.midominio.com \
    -d api.midominio.com

# Copiar certificados
sudo cp /etc/letsencrypt/live/midominio.com/fullchain.pem ssl/cert.pem
sudo cp /etc/letsencrypt/live/midominio.com/privkey.pem ssl/key.pem
sudo chown $(whoami):$(whoami) ssl/cert.pem ssl/key.pem
```

#### Certificado auto-firmado (Desarrollo)
```bash
mkdir -p ssl
openssl genrsa -out ssl/key.pem 2048
openssl req -new -x509 -key ssl/key.pem -out ssl/cert.pem -days 365 \
    -subj "/C=AR/ST=Cordoba/L=Cordoba/O=Tuchanga/CN=midominio.com" \
    -addext "subjectAltName=DNS:midominio.com,DNS:www.midominio.com,DNS:api.midominio.com"
```

### 4. Reiniciar aplicación

```bash
docker-compose down
docker-compose up -d --build
```

## 🧪 Verificación

### Comandos de prueba

```bash
# Verificar DNS
nslookup midominio.com
nslookup www.midominio.com
nslookup api.midominio.com

# Verificar HTTPS
curl -I https://midominio.com
curl -I https://www.midominio.com
curl -I https://api.midominio.com

# Verificar API
curl https://api.midominio.com/health
```

### URLs finales

- **Frontend**: https://midominio.com
- **Frontend WWW**: https://www.midominio.com
- **API**: https://api.midominio.com

## 🔄 Renovación de Certificados

### Configurar renovación automática (Let's Encrypt)

```bash
# Editar crontab
sudo crontab -e

# Agregar línea para renovación automática
0 12 * * * /usr/bin/certbot renew --quiet && docker-compose restart frontend
```

## 🛠️ Troubleshooting

### Problema: DNS no resuelve
**Solución**: 
- Verificar registros DNS en el panel de tu hosting
- Esperar propagación DNS (5-30 minutos)
- Usar herramientas online: whatsmydns.net

### Problema: Error SSL
**Solución**:
```bash
# Verificar certificados
openssl x509 -in ssl/cert.pem -text -noout

# Regenerar certificados
./setup-custom-domain.sh
```

### Problema: API no responde
**Solución**:
```bash
# Verificar logs del backend
docker-compose logs backend

# Verificar configuración nginx
docker-compose logs frontend

# Reiniciar servicios
docker-compose restart
```

### Problema: CORS errors
**Solución**: Verificar que el archivo `.env` tenga:
```
CORS_ORIGIN=https://midominio.com,https://www.midominio.com
```

## 📱 Configuración por Hosting

### cPanel
1. Ir a "Zone Editor" o "DNS Zone Editor"
2. Agregar registros A como se muestra arriba
3. Guardar cambios

### Plesk
1. Ir a "DNS Settings"
2. Agregar registros A
3. Aplicar cambios

### Cloudflare
1. Ir a "DNS" en el dashboard
2. Agregar registros A
3. Asegurar que el proxy esté desactivado (nube gris)

### GoDaddy
1. Ir a "DNS Management"
2. Agregar registros A
3. Guardar cambios

## 🎉 ¡Listo!

Tu aplicación Tuchanga ahora está disponible en:
- https://midominio.com (Frontend)
- https://api.midominio.com (API)

Para verificar que todo funciona correctamente, ejecuta:
```bash
./test-domain-setup.sh
```
