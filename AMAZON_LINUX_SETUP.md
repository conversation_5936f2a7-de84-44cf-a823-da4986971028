# Amazon Linux Setup Guide - Tuchang<PERSON> Job Platform

Esta guía está específicamente diseñada para instalar y configurar Tuchanga Job Platform en Amazon Linux EC2.

## 🚀 Instalación Rápida (Un Solo Comando)

Para una instalación completa automática:

```bash
# Hacer el script ejecutable
chmod +x quick-install-amazon-linux.sh

# Ejecutar instalación completa
./quick-install-amazon-linux.sh
```

Este script:
- ✅ Actualiza el sistema
- ✅ Instala Docker y Docker Compose
- ✅ Genera certificados SSL
- ✅ Despliega la aplicación con HTTPS
- ✅ Verifica el funcionamiento

## 🔧 Instalación Paso a Paso

### 1. Preparación del Sistema

```bash
# Verificar configuración de AWS
./check-aws-setup.sh

# Instalar dependencias del sistema
./setup-amazon-linux.sh
```

### 2. Configurar Security Groups

Asegúrate de que tu Security Group de EC2 permita:

| Puerto | Protocolo | Origen    | Descripción |
|--------|-----------|-----------|-------------|
| 22     | TCP       | Tu IP     | SSH         |
| 80     | TCP       | 0.0.0.0/0 | HTTP        |
| 443    | TCP       | 0.0.0.0/0 | HTTPS       |
| 3000   | TCP       | 0.0.0.0/0 | Backend API |

### 3. Despliegue con HTTPS

```bash
# Opción 1: Despliegue rápido con certificados auto-firmados
./deploy-https.sh

# Opción 2: Configuración completa con Let's Encrypt
./setup-https.sh
```

## 🐧 Comandos Específicos para Amazon Linux

### Gestión de Servicios

```bash
# Docker
sudo systemctl start docker
sudo systemctl enable docker
sudo systemctl status docker

# Verificar instalación
docker --version
docker-compose --version
```

### Gestión de Paquetes

```bash
# Actualizar sistema
sudo yum update -y

# Instalar paquetes adicionales
sudo yum install -y htop nano vim git

# Buscar paquetes
yum search nombre-paquete

# Información de paquete
yum info nombre-paquete
```

### Configuración de Usuario

```bash
# Agregar usuario al grupo docker
sudo usermod -a -G docker ec2-user

# Verificar grupos del usuario
groups

# Aplicar cambios de grupo (requiere re-login)
newgrp docker
```

## 🔐 Configuración SSL/TLS

### Certificados Auto-firmados (Desarrollo)

```bash
# Generar certificados rápidamente
./generate-ssl.sh

# Verificar certificados
openssl x509 -in ssl/cert.pem -text -noout
```

### Let's Encrypt (Producción)

```bash
# Instalar certbot
sudo pip3 install certbot

# Obtener certificado
sudo certbot certonly --standalone \
  --email <EMAIL> \
  --agree-tos \
  -d ec2-18-231-110-178.sa-east-1.compute.amazonaws.com

# Configurar renovación automática
sudo crontab -e
# Agregar: 0 12 * * * /usr/local/bin/certbot renew --quiet
```

## 🌐 Configuración de Red

### Verificar Conectividad

```bash
# Probar conectividad externa
curl -I https://google.com

# Verificar puertos locales
netstat -tlnp | grep -E ':(80|443|3000)'

# Probar puertos desde fuera
telnet tu-ip-publica 80
telnet tu-ip-publica 443
telnet tu-ip-publica 3000
```

### Configurar Firewall (si está habilitado)

```bash
# Verificar estado del firewall
sudo systemctl status firewalld

# Si está activo, abrir puertos
sudo firewall-cmd --permanent --add-port=80/tcp
sudo firewall-cmd --permanent --add-port=443/tcp
sudo firewall-cmd --permanent --add-port=3000/tcp
sudo firewall-cmd --reload
```

## 🐳 Gestión de Docker

### Comandos Básicos

```bash
# Ver contenedores
docker ps
docker-compose ps

# Ver logs
docker-compose logs
docker-compose logs -f frontend
docker-compose logs -f backend

# Reiniciar servicios
docker-compose restart
docker-compose restart frontend

# Reconstruir y reiniciar
docker-compose up -d --build

# Limpiar sistema
docker system prune -f
docker volume prune -f
```

### Monitoreo de Recursos

```bash
# Uso de recursos por contenedor
docker stats

# Información del sistema
htop
free -h
df -h

# Logs del sistema
sudo journalctl -u docker
```

## 🔍 Solución de Problemas

### Problemas Comunes

#### 1. "Permission denied" al usar Docker

```bash
# Verificar grupo docker
groups

# Si no está en el grupo
sudo usermod -a -G docker $(whoami)

# Cerrar sesión y volver a entrar
exit
# ssh de nuevo
```

#### 2. Puertos no accesibles

```bash
# Verificar Security Groups
./check-aws-setup.sh

# Verificar firewall local
sudo systemctl status firewalld

# Verificar que los contenedores estén corriendo
docker-compose ps
```

#### 3. Certificados SSL inválidos

```bash
# Regenerar certificados
rm -rf ssl/
./generate-ssl.sh

# Verificar certificados
openssl x509 -in ssl/cert.pem -text -noout
```

#### 4. Contenedores no inician

```bash
# Ver logs detallados
docker-compose logs

# Verificar recursos del sistema
free -h
df -h

# Reiniciar Docker
sudo systemctl restart docker
```

### Logs Importantes

```bash
# Logs de Docker
sudo journalctl -u docker -f

# Logs de la aplicación
docker-compose logs -f

# Logs del sistema
sudo tail -f /var/log/messages

# Logs de nginx (dentro del contenedor)
docker-compose exec frontend tail -f /var/log/nginx/error.log
```

## 📊 Monitoreo y Mantenimiento

### Verificación de Salud

```bash
# Script de verificación completa
./check-aws-setup.sh

# Probar endpoints
curl -k https://localhost:443
curl http://localhost:3000/health

# Verificar base de datos
docker-compose exec postgres psql -U postgres -d job_platform -c "SELECT version();"
```

### Backup y Restauración

```bash
# Backup de base de datos
docker-compose exec postgres pg_dump -U postgres job_platform > backup-$(date +%Y%m%d).sql

# Backup de certificados SSL
tar -czf ssl-backup-$(date +%Y%m%d).tar.gz ssl/

# Restaurar base de datos
docker-compose exec -T postgres psql -U postgres job_platform < backup-20241201.sql
```

## 🚀 Optimización para Producción

### Configuración de Memoria

```bash
# Verificar memoria disponible
free -h

# Configurar swap si es necesario
sudo dd if=/dev/zero of=/swapfile bs=1M count=1024
sudo chmod 600 /swapfile
sudo mkswap /swapfile
sudo swapon /swapfile
```

### Configuración de Logs

```bash
# Configurar rotación de logs de Docker
sudo tee /etc/docker/daemon.json > /dev/null <<EOF
{
  "log-driver": "json-file",
  "log-opts": {
    "max-size": "10m",
    "max-file": "3"
  }
}
EOF

sudo systemctl restart docker
```

## 📋 Checklist de Deployment

- [ ] ✅ Sistema actualizado (`sudo yum update -y`)
- [ ] ✅ Docker instalado y funcionando
- [ ] ✅ Docker Compose instalado
- [ ] ✅ Usuario en grupo docker
- [ ] ✅ Security Groups configurados (80, 443, 3000)
- [ ] ✅ Certificados SSL generados
- [ ] ✅ Aplicación desplegada (`docker-compose up -d`)
- [ ] ✅ Contenedores funcionando (`docker-compose ps`)
- [ ] ✅ HTTPS accesible desde internet
- [ ] ✅ Backend API respondiendo
- [ ] ✅ Base de datos conectada

## 🆘 Soporte

Si encuentras problemas:

1. **Ejecuta el diagnóstico**: `./check-aws-setup.sh`
2. **Revisa los logs**: `docker-compose logs`
3. **Verifica la red**: `netstat -tlnp`
4. **Comprueba recursos**: `htop` y `df -h`

Para más ayuda, revisa los logs específicos de cada servicio y asegúrate de que todos los puertos estén abiertos en AWS Security Groups.
