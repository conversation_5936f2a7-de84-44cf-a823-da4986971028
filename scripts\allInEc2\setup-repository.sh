#!/bin/bash

# Setup Git repository for Tuchanga project
# This script updates the existing repository and installs dependencies

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to check if command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_DIR="$(cd "$SCRIPT_DIR/../.." && pwd)"
BACKUP_DIR="/tmp/tuchanga-backup-$(date +%Y%m%d-%H%M%S)"

print_status "Directorio del proyecto detectado: $PROJECT_DIR"

# Check if Git is installed
check_git() {
    if ! command_exists git; then
        print_error "Git no está instalado. Ejecuta install-dependencies.sh primero"
        exit 1
    fi
}

# Verify project structure
verify_project_structure() {
    print_status "Verificando estructura del proyecto..."

    if [[ ! -d "$PROJECT_DIR" ]]; then
        print_error "Directorio del proyecto no encontrado: $PROJECT_DIR"
        exit 1
    fi

    # Check if we're in a git repository
    if [[ ! -d "$PROJECT_DIR/.git" ]]; then
        print_error "No se detectó un repositorio Git en $PROJECT_DIR"
        print_error "Asegúrate de estar ejecutando desde dentro del repositorio clonado"
        exit 1
    fi

    # Check for essential directories
    local required_dirs=("backend" "frontend" "scripts")
    local missing_dirs=0

    for dir in "${required_dirs[@]}"; do
        if [[ -d "$PROJECT_DIR/$dir" ]]; then
            print_success "Directorio $dir encontrado"
        else
            print_error "Directorio requerido no encontrado: $dir"
            ((missing_dirs++))
        fi
    done

    if [[ $missing_dirs -gt 0 ]]; then
        print_error "Estructura del proyecto incompleta"
        exit 1
    fi

    print_success "Estructura del proyecto verificada"
}

# Update repository if needed
update_repository() {
    print_status "Verificando estado del repositorio..."

    cd "$PROJECT_DIR"

    # Get current branch and remote info
    local current_branch=$(git branch --show-current)
    local remote_url=$(git remote get-url origin 2>/dev/null || echo "No remote configured")

    print_status "Información del repositorio:"
    echo "- Directorio: $PROJECT_DIR"
    echo "- Rama actual: $current_branch"
    echo "- Remoto: $remote_url"
    echo "- Último commit: $(git log -1 --pretty=format:'%h - %s (%an, %ar)' 2>/dev/null || echo 'No commits')"

    # Check if there are uncommitted changes
    if ! git diff-index --quiet HEAD -- 2>/dev/null; then
        print_warning "Hay cambios no confirmados en el repositorio"
        read -p "¿Deseas hacer stash de los cambios y actualizar? (y/n): " update_confirm

        if [[ $update_confirm == "y" ]]; then
            print_status "Guardando cambios locales..."
            git stash push -m "Auto-stash before deployment $(date)"

            print_status "Actualizando desde remoto..."
            git fetch origin
            git pull origin "$current_branch" || print_warning "No se pudo actualizar desde remoto"

            print_success "Repositorio actualizado"
        else
            print_warning "Continuando con la versión actual del código"
        fi
    else
        print_status "Actualizando desde remoto..."
        if git fetch origin && git pull origin "$current_branch" 2>/dev/null; then
            print_success "Repositorio actualizado desde remoto"
        else
            print_warning "No se pudo actualizar desde remoto (continuando con versión local)"
        fi
    fi

    # Set proper permissions
    sudo chown -R $USER:$USER "$PROJECT_DIR"
}

# Setup environment files
setup_environment_files() {
    print_status "Configurando archivos de entorno..."
    
    cd "$PROJECT_DIR"
    
    # Copy production environment file if it doesn't exist
    if [[ ! -f ".env" ]]; then
        if [[ -f ".env.production" ]]; then
            print_status "Copiando .env.production a .env..."
            cp .env.production .env
        elif [[ -f ".env.example" ]]; then
            print_status "Copiando .env.example a .env..."
            cp .env.example .env
        else
            print_warning "No se encontró archivo de entorno base"
            print_status "Creando archivo .env básico..."
            cat > .env << EOF
# Tuchanga Production Environment
NODE_ENV=production
DB_HOST=postgres
DB_PORT=5432
DB_USERNAME=postgres
DB_PASSWORD=TuChanga2024SecureDB
DB_DATABASE=tuchanga
DATABASE_URL=********************************************************/tuchanga
JWT_SECRET=TuChanga2024SuperSecretJWTKeyChangeInProduction
JWT_EXPIRES_IN=24h
FIREBASE_PROJECT_ID=tu-changa-583b3
CORS_ORIGIN=*
API_PREFIX=
EOF
        fi
        print_success "Archivo .env configurado"
    else
        print_success "Archivo .env ya existe"
    fi
    
    # Set proper permissions for environment file
    chmod 600 .env
}

# Install project dependencies
install_dependencies() {
    print_status "Instalando dependencias del proyecto..."
    
    cd "$PROJECT_DIR"
    
    # Install backend dependencies
    if [[ -d "backend" ]] && [[ -f "backend/package.json" ]]; then
        print_status "Instalando dependencias del backend..."
        cd backend
        npm ci --production
        cd ..
        print_success "Dependencias del backend instaladas"
    fi
    
    # Install frontend dependencies
    if [[ -d "frontend" ]] && [[ -f "frontend/package.json" ]]; then
        print_status "Instalando dependencias del frontend..."
        cd frontend
        npm ci
        cd ..
        print_success "Dependencias del frontend instaladas"
    fi
}

# Main function
main() {
    print_status "Configurando proyecto Tuchanga..."
    echo ""

    check_git
    verify_project_structure
    update_repository
    setup_environment_files
    install_dependencies

    echo ""
    print_success "¡Proyecto configurado exitosamente!"
    echo ""
    print_status "Próximos pasos:"
    echo "1. Configurar base de datos: ./setup-database.sh"
    echo "2. Configurar Docker: ./setup-docker.sh"
    echo "3. Configurar SSL: ./setup-ssl.sh"
    echo "4. Iniciar aplicación: ./start-application.sh"
    echo ""
}

# Run main function
main "$@"
