# 💰 TuChanga - Guía de Optimización de Costos AWS

## 🎯 Objetivo: Lanzamiento con Costos Mínimos

Esta guía te ayuda a lanzar TuChanga con el menor costo posible, aprovechando al máximo el free tier de AWS y optimizaciones para startups.

## 📊 Comparación de Costos

### **Configuración Estándar vs Optimizada**

| Componente | Estándar | Optimizada | Ahorro |
|------------|----------|------------|--------|
| **Base de Datos** | Aurora PostgreSQL (db.t3.medium) | RDS PostgreSQL (db.t3.micro) | ~$40/mes |
| **Compute** | ECS Fargate (múltiples tasks) | ECS Fargate (1 task c/u) | ~$10/mes |
| **Storage** | 100GB | 20GB (Free Tier) | ~$8/mes |
| **Multi-AZ** | Habilitado | Deshabilitado | ~$15/mes |
| **Backup** | 7 días | 7 días (Free Tier) | $0 |
| **Total** | **~$85/mes** | **~$35/mes** | **~$50/mes** |

### **Con Free Tier (Primer Año)**

| Componente | Costo con Free Tier |
|------------|-------------------|
| RDS db.t3.micro | **$0** (750 horas/mes gratis) |
| ECS Fargate | **~$15/mes** (no incluido en free tier) |
| ALB | **~$20/mes** (no incluido en free tier) |
| Storage 20GB | **$0** (20GB gratis) |
| **Total Primer Año** | **~$35/mes** |

## 🚀 Scripts Optimizados

### **1. Deploy Mínimo**
```bash
# Usar scripts optimizados para costos
./01-full-deploy-minimal.sh
./02-deploy-containers-minimal.sh
```

### **2. Diferencias Clave**

#### **Base de Datos:**
- ✅ **RDS PostgreSQL** en lugar de Aurora
- ✅ **db.t3.micro** (Free Tier eligible)
- ✅ **Single-AZ** (no Multi-AZ)
- ✅ **20GB storage** (Free Tier)

#### **ECS:**
- ✅ **1 task por servicio** (mínima redundancia)
- ✅ **256 CPU, 512MB RAM** (mínimo viable)
- ✅ **Sin auto-scaling** inicial

#### **Networking:**
- ✅ **ALB necesario** para HTTPS (costo inevitable)
- ✅ **VPC endpoints** omitidos (usar NAT Gateway solo si necesario)

## 🔧 Configuración Free Tier

### **Recursos Incluidos en Free Tier (12 meses):**

#### **RDS PostgreSQL:**
- ✅ 750 horas/mes de db.t3.micro
- ✅ 20GB de storage SSD
- ✅ 20GB de backup storage
- ✅ 10 millones de I/Os

#### **ECS Fargate:**
- ❌ **No incluido** en free tier
- 💡 Considera EC2 t3.micro (750 horas gratis) para mayor ahorro

#### **CloudWatch:**
- ✅ 10 métricas personalizadas
- ✅ 5GB de logs
- ✅ 3 dashboards

#### **ALB:**
- ❌ **No incluido** en free tier (~$20/mes inevitable)

## 💡 Estrategias de Ahorro Adicionales

### **1. Alternativa Ultra-Económica: EC2 + Docker Compose**

Si quieres ahorrar aún más (~$8-12/mes):

```bash
# Usar EC2 t3.micro (Free Tier) con Docker Compose
# Scripts existentes en la raíz del proyecto
./start-containers-step-by-step.sh
```

**Pros:**
- ✅ EC2 t3.micro gratis (750 horas/mes)
- ✅ PostgreSQL en contenedor (gratis)
- ✅ Solo pagas por storage y transferencia

**Contras:**
- ❌ Menos escalable
- ❌ Más mantenimiento manual
- ❌ Sin alta disponibilidad

### **2. Optimizaciones Progresivas**

#### **Fase 1: Lanzamiento (Mes 1-6)**
```bash
# Configuración mínima
./01-full-deploy-minimal.sh
```
- 1 task backend, 1 task frontend
- RDS db.t3.micro
- Sin auto-scaling

#### **Fase 2: Crecimiento (Mes 6-12)**
```bash
# Escalar servicios
aws ecs update-service --cluster tuchanga-cluster --service tuchanga-backend-service --desired-count 2
aws ecs update-service --cluster tuchanga-cluster --service tuchanga-frontend-service --desired-count 2
```

#### **Fase 3: Escalamiento (Año 2+)**
```bash
# Migrar a Aurora cuando tengas más usuarios
./01-full-deploy.sh  # Configuración completa
```

## 📈 Escalamiento Gradual

### **Comandos para Escalar Cuando Necesites:**

#### **Aumentar Tasks:**
```bash
# Escalar backend a 2 tasks
aws ecs update-service \
  --cluster tuchanga-cluster \
  --service tuchanga-backend-service \
  --desired-count 2

# Escalar frontend a 2 tasks  
aws ecs update-service \
  --cluster tuchanga-cluster \
  --service tuchanga-frontend-service \
  --desired-count 2
```

#### **Aumentar Recursos:**
```bash
# Actualizar task definition con más CPU/memoria
# Editar task definition: 512 CPU, 1024MB RAM
./04-update-deployment.sh backend
```

#### **Migrar a Aurora:**
```bash
# Cuando tengas más usuarios y presupuesto
# 1. Backup de RDS
./03-backup-database.sh

# 2. Crear Aurora cluster
# 3. Restaurar datos
# 4. Actualizar variables de entorno
```

## 🔍 Monitoreo de Costos

### **1. Configurar Billing Alerts:**
```bash
# Crear alerta de facturación
aws budgets create-budget \
  --account-id $(aws sts get-caller-identity --query Account --output text) \
  --budget '{
    "BudgetName": "TuChanga-Monthly-Budget",
    "BudgetLimit": {
      "Amount": "50",
      "Unit": "USD"
    },
    "TimeUnit": "MONTHLY",
    "BudgetType": "COST"
  }'
```

### **2. Revisar Costos Regularmente:**
- AWS Cost Explorer
- AWS Billing Dashboard
- CloudWatch metrics

## 🛡️ Consideraciones de Seguridad vs Costo

### **Optimizaciones Seguras:**
- ✅ Mantener Secrets Manager (costo mínimo)
- ✅ Mantener VPC y Security Groups (gratis)
- ✅ Mantener CloudWatch Logs básicos (Free Tier)

### **Optimizaciones Riesgosas (NO recomendadas):**
- ❌ Eliminar backups
- ❌ Usar contraseñas hardcodeadas
- ❌ Exponer base de datos públicamente

## 📅 Roadmap de Costos

### **Mes 1-3: Validación (Free Tier)**
- Costo: ~$35/mes
- Usuarios: 0-100
- Configuración: Mínima

### **Mes 4-12: Crecimiento Inicial**
- Costo: ~$50-80/mes
- Usuarios: 100-1000
- Configuración: Escalada gradual

### **Año 2+: Escalamiento**
- Costo: ~$100-300/mes
- Usuarios: 1000+
- Configuración: Aurora, Auto-scaling, CDN

## 🎯 Recomendación Final

**Para el lanzamiento inicial:**

1. **Usa los scripts optimizados:**
   ```bash
   ./01-full-deploy-minimal.sh
   ```

2. **Aprovecha Free Tier al máximo**

3. **Monitorea costos semanalmente**

4. **Escala gradualmente según usuarios**

5. **Migra a configuración completa cuando tengas ingresos**

**¡Lanza con ~$35/mes y escala según crezca tu negocio! 🚀**
