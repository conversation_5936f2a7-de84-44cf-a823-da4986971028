#!/bin/bash

# Check status of Tuchanga application
# This script provides comprehensive status information

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m'

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_header() {
    echo -e "${PURPLE}================================${NC}"
    echo -e "${PURPLE}$1${NC}"
    echo -e "${PURPLE}================================${NC}"
}

# Configuration
PROJECT_DIR="/opt/tuchanga"

# Function to check if command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Check system resources
check_system_resources() {
    print_header "RECURSOS DEL SISTEMA"
    
    # CPU usage
    local cpu_usage=$(top -bn1 | grep "Cpu(s)" | awk '{print $2}' | cut -d'%' -f1)
    echo "🖥️  CPU Usage: ${cpu_usage}%"
    
    # Memory usage
    local mem_info=$(free -h | awk 'NR==2{printf "Used: %s/%s (%.1f%%)", $3,$2,$3*100/$2}')
    echo "💾 Memory: $mem_info"
    
    # Disk usage
    local disk_usage=$(df -h / | awk 'NR==2{printf "%s/%s (%s)", $3,$2,$5}')
    echo "💿 Disk: $disk_usage"
    
    # Load average
    local load_avg=$(uptime | awk -F'load average:' '{print $2}')
    echo "⚖️  Load Average:$load_avg"
    
    # Uptime
    local uptime_info=$(uptime -p)
    echo "⏰ Uptime: $uptime_info"
    
    echo ""
}

# Check PM2 status
check_pm2_status() {
    print_header "ESTADO DE PM2"

    if command_exists pm2; then
        echo "✅ PM2: Installed"
        echo "📦 PM2 Version: $(pm2 --version)"

        # PM2 process info
        local processes_running=$(pm2 list | grep -c "online" || echo "0")
        local processes_total=$(pm2 list | grep -c "│" | tail -1 || echo "0")

        echo "🔄 Processes: $processes_running running / $processes_total total"

        # Show PM2 processes
        echo ""
        echo "📋 PM2 Processes:"
        pm2 list 2>/dev/null || echo "   No processes found"

    else
        echo "❌ PM2: Not installed"
    fi

    echo ""
}

# Check Tuchanga processes
check_processes() {
    print_header "PROCESOS TUCHANGA"

    # Check PM2 processes
    if command_exists pm2; then
        local tuchanga_processes=$(pm2 list | grep tuchanga || echo "")

        if [[ -n "$tuchanga_processes" ]]; then
            echo "📦 Procesos PM2 de Tuchanga:"
            echo "$tuchanga_processes"

            # Show detailed info for each process
            pm2 show tuchanga-backend 2>/dev/null | grep -E "(status|uptime|cpu|memory)" || echo "   No hay información detallada disponible"
        else
            echo "⚪ No hay procesos de Tuchanga ejecutándose en PM2"
        fi
    else
        echo "❌ PM2 no está disponible"
    fi

    # Check PostgreSQL
    if systemctl is-active --quiet postgresql; then
        echo "✅ PostgreSQL: Running"
        local pg_uptime=$(systemctl show postgresql --property=ActiveEnterTimestamp --value | cut -d' ' -f2-3)
        echo "   ⏰ Uptime: since $pg_uptime"
    else
        echo "❌ PostgreSQL: Not running"
    fi

    echo ""
}

# Check services
check_services() {
    print_header "SERVICIOS DEL SISTEMA"

    local services=("nginx" "postgresql")

    for service in "${services[@]}"; do
        if systemctl is-active --quiet "$service"; then
            local status=$(systemctl is-active "$service")
            local enabled=$(systemctl is-enabled "$service" 2>/dev/null || echo "unknown")
            echo "✅ $service: $status (enabled: $enabled)"
        else
            echo "❌ $service: inactive"
        fi
    done

    echo ""
}

# Check network connectivity
check_network() {
    print_header "CONECTIVIDAD DE RED"
    
    # Check if ports are listening
    local ports=("80" "443" "3000" "5432")
    
    for port in "${ports[@]}"; do
        if netstat -tuln | grep -q ":$port "; then
            local service=$(netstat -tuln | grep ":$port " | awk '{print $1}' | head -1)
            echo "✅ Port $port: Listening ($service)"
        else
            echo "❌ Port $port: Not listening"
        fi
    done
    
    echo ""
    
    # Test external connectivity
    print_status "Testing external connectivity..."
    if curl -s --max-time 5 https://google.com >/dev/null; then
        echo "✅ Internet: Connected"
    else
        echo "❌ Internet: No connection"
    fi
    
    echo ""
}

# Check database
check_database() {
    print_header "BASE DE DATOS"

    if systemctl is-active --quiet postgresql; then
        # Test database connection
        if sudo -u postgres psql -d tuchanga -c "SELECT 1;" >/dev/null 2>&1; then
            echo "✅ Database: Connected and ready"

            # Get database info
            local db_size=$(sudo -u postgres psql -d tuchanga -t -c "SELECT pg_size_pretty(pg_database_size('tuchanga'));" 2>/dev/null | xargs || echo "N/A")
            local tables_count=$(sudo -u postgres psql -d tuchanga -t -c "SELECT count(*) FROM information_schema.tables WHERE table_schema = 'public';" 2>/dev/null | xargs || echo "N/A")
            local connections=$(sudo -u postgres psql -d tuchanga -t -c "SELECT count(*) FROM pg_stat_activity WHERE datname = 'tuchanga';" 2>/dev/null | xargs || echo "N/A")

            echo "📏 Database Size: $db_size"
            echo "📋 Tables: $tables_count"
            echo "🔗 Active Connections: $connections"
            echo "🏠 Host: localhost:5432"

        else
            echo "❌ Database: Not responding"
        fi
    else
        echo "❌ Database: PostgreSQL service not running"
    fi

    echo ""
}

# Check backend API
check_backend() {
    print_header "BACKEND API"

    if pm2 list | grep -q "tuchanga-backend.*online"; then
        # Test health endpoint
        if curl -f http://localhost:3000/health >/dev/null 2>&1; then
            echo "✅ Backend: Healthy"

            # Get API info
            local response=$(curl -s http://localhost:3000/health 2>/dev/null || echo "{}")
            echo "📊 Health Check Response: $response"
            echo "🔌 Port: 3000"

        else
            echo "❌ Backend: Health check failed"

            # Show recent logs
            print_status "Recent backend logs:"
            pm2 logs tuchanga-backend --lines 5 2>/dev/null || echo "No logs available"
        fi
    else
        echo "❌ Backend: PM2 process not running"

        # Check if PM2 process exists but is stopped
        if pm2 list | grep -q "tuchanga-backend"; then
            echo "⚠️  Backend process exists but is stopped"
        fi
    fi

    echo ""
}

# Check frontend/nginx
check_frontend() {
    print_header "FRONTEND / NGINX"
    
    if systemctl is-active --quiet nginx; then
        echo "✅ Nginx: Running"
        
        # Test HTTP redirect
        local http_status=$(curl -s -o /dev/null -w "%{http_code}" http://localhost/ 2>/dev/null || echo "000")
        if [[ "$http_status" == "301" ]] || [[ "$http_status" == "302" ]]; then
            echo "✅ HTTP Redirect: Working ($http_status)"
        else
            echo "❌ HTTP Redirect: Not working ($http_status)"
        fi
        
        # Test HTTPS
        local https_status=$(curl -s -o /dev/null -w "%{http_code}" -k https://localhost/ 2>/dev/null || echo "000")
        if [[ "$https_status" == "200" ]]; then
            echo "✅ HTTPS: Working ($https_status)"
        else
            echo "❌ HTTPS: Not working ($https_status)"
        fi
        
        # Check static files
        if [[ -d "/var/www/tuchanga" ]] && [[ -f "/var/www/tuchanga/index.html" ]]; then
            local files_count=$(find /var/www/tuchanga -type f | wc -l)
            echo "📁 Static Files: $files_count files deployed"
        else
            echo "❌ Static Files: Not deployed"
        fi
        
    else
        echo "❌ Nginx: Not running"
    fi
    
    echo ""
}

# Check SSL certificates
check_ssl() {
    print_header "CERTIFICADOS SSL"
    
    local ssl_dir="$PROJECT_DIR/ssl"
    
    if [[ -f "$ssl_dir/cert.pem" ]] && [[ -f "$ssl_dir/key.pem" ]]; then
        # Validate certificate
        if openssl x509 -in "$ssl_dir/cert.pem" -text -noout >/dev/null 2>&1; then
            echo "✅ SSL Certificate: Valid"
            
            # Get certificate info
            local subject=$(openssl x509 -in "$ssl_dir/cert.pem" -noout -subject | sed 's/subject= //')
            local issuer=$(openssl x509 -in "$ssl_dir/cert.pem" -noout -issuer | sed 's/issuer= //')
            local not_after=$(openssl x509 -in "$ssl_dir/cert.pem" -noout -enddate | sed 's/notAfter=//')
            
            echo "📋 Subject: $subject"
            echo "🏢 Issuer: $issuer"
            echo "📅 Expires: $not_after"
            
            # Calculate days until expiry
            local expiry_epoch=$(date -d "$not_after" +%s)
            local current_epoch=$(date +%s)
            local days_until_expiry=$(( (expiry_epoch - current_epoch) / 86400 ))
            
            if [[ $days_until_expiry -gt 30 ]]; then
                echo "✅ Expiry: $days_until_expiry days remaining"
            elif [[ $days_until_expiry -gt 0 ]]; then
                echo "⚠️  Expiry: $days_until_expiry days remaining (renewal needed soon)"
            else
                echo "❌ Expiry: Certificate expired"
            fi
            
        else
            echo "❌ SSL Certificate: Invalid"
        fi
    else
        echo "❌ SSL Certificate: Not found"
    fi
    
    echo ""
}

# Check logs for errors
check_logs() {
    print_header "ERRORES RECIENTES EN LOGS"
    
    # Check Docker container logs for errors
    local containers=("tuchanga-postgres" "tuchanga-backend")
    
    for container in "${containers[@]}"; do
        if docker ps --format "{{.Names}}" | grep -q "$container"; then
            local errors=$(docker logs "$container" --since 1h 2>&1 | grep -i "error\|exception\|failed" | tail -3)
            if [[ -n "$errors" ]]; then
                echo "❌ $container errors:"
                echo "$errors" | sed 's/^/   /'
            else
                echo "✅ $container: No recent errors"
            fi
        fi
    done
    
    # Check Nginx error log
    if [[ -f "/var/log/nginx/error.log" ]]; then
        local nginx_errors=$(tail -n 50 /var/log/nginx/error.log | grep "$(date '+%Y/%m/%d')" | tail -3)
        if [[ -n "$nginx_errors" ]]; then
            echo "❌ Nginx errors today:"
            echo "$nginx_errors" | sed 's/^/   /'
        else
            echo "✅ Nginx: No errors today"
        fi
    fi
    
    echo ""
}

# Show summary
show_summary() {
    print_header "RESUMEN DEL ESTADO"
    
    local issues=0
    
    # Check critical services
    if ! systemctl is-active --quiet nginx; then
        echo "❌ Nginx no está ejecutándose"
        ((issues++))
    fi
    
    if ! docker ps --format "{{.Names}}" | grep -q "tuchanga-postgres"; then
        echo "❌ Base de datos no está ejecutándose"
        ((issues++))
    fi
    
    if ! docker ps --format "{{.Names}}" | grep -q "tuchanga-backend"; then
        echo "❌ Backend no está ejecutándose"
        ((issues++))
    fi
    
    if ! curl -f http://localhost:3000/health >/dev/null 2>&1; then
        echo "❌ Backend no responde al health check"
        ((issues++))
    fi
    
    if [[ $issues -eq 0 ]]; then
        echo "✅ Todos los servicios están funcionando correctamente"
        echo ""
        print_success "🎉 ¡Tuchanga está completamente operativo!"
    else
        echo ""
        print_warning "⚠️  Se encontraron $issues problemas que requieren atención"
        echo ""
        print_status "Comandos para solucionar problemas:"
        echo "- Reiniciar servicios: ./restart-services.sh"
        echo "- Ver logs detallados: ./show-logs.sh"
        echo "- Reiniciar aplicación: ./start-application.sh"
    fi
    
    echo ""
}

# Main function
main() {
    clear
    print_header "TUCHANGA - VERIFICACIÓN DE ESTADO"
    echo "Fecha: $(date)"
    echo ""
    
    check_system_resources
    check_pm2_status
    check_processes
    check_services
    check_network
    check_database
    check_backend
    check_frontend
    check_ssl
    check_logs
    show_summary
}

# Run main function
main "$@"
