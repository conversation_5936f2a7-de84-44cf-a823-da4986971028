#!/bin/bash

# Setup SSL certificates for Tuchanga
# This script configures SSL certificates using Let's Encrypt or self-signed certificates

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to check if command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Configuration
DOMAIN="$1"
EMAIL="$2"
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_DIR="$(cd "$SCRIPT_DIR/../.." && pwd)"
SSL_DIR="$PROJECT_DIR/ssl"

print_status "Directorio del proyecto detectado: $PROJECT_DIR"

# Validate inputs
if [[ -z "$DOMAIN" ]]; then
    print_error "Dominio es requerido"
    echo "Uso: $0 <domain> <email>"
    echo "Ejemplo: $0 midominio.com <EMAIL>"
    exit 1
fi

if [[ -z "$EMAIL" ]]; then
    print_error "Email es requerido"
    echo "Uso: $0 <domain> <email>"
    echo "Ejemplo: $0 midominio.com <EMAIL>"
    exit 1
fi

# Check prerequisites
check_prerequisites() {
    if ! command_exists certbot; then
        print_error "Certbot no está instalado. Ejecuta install-dependencies.sh primero"
        exit 1
    fi
    
    if [[ ! -d "$PROJECT_DIR" ]]; then
        print_error "Directorio del proyecto no encontrado: $PROJECT_DIR"
        print_error "Ejecuta setup-repository.sh primero"
        exit 1
    fi
}

# Create SSL directory
create_ssl_directory() {
    print_status "Creando directorio SSL..."
    
    mkdir -p "$SSL_DIR"
    chmod 755 "$SSL_DIR"
    
    print_success "Directorio SSL creado: $SSL_DIR"
}

# Check if certificates exist
certificates_exist() {
    [[ -f "$SSL_DIR/cert.pem" ]] && [[ -f "$SSL_DIR/key.pem" ]]
}

# Validate existing certificates
validate_certificates() {
    if certificates_exist; then
        print_status "Validando certificados existentes..."
        
        # Check certificate validity
        if openssl x509 -in "$SSL_DIR/cert.pem" -text -noout >/dev/null 2>&1; then
            # Check expiration date
            local expiry_date=$(openssl x509 -in "$SSL_DIR/cert.pem" -noout -enddate | cut -d= -f2)
            local expiry_epoch=$(date -d "$expiry_date" +%s)
            local current_epoch=$(date +%s)
            local days_until_expiry=$(( (expiry_epoch - current_epoch) / 86400 ))
            
            if [[ $days_until_expiry -gt 30 ]]; then
                print_success "Certificados válidos (expiran en $days_until_expiry días)"
                return 0
            else
                print_warning "Certificados expiran pronto ($days_until_expiry días)"
                return 1
            fi
        else
            print_warning "Certificados existentes son inválidos"
            return 1
        fi
    else
        return 1
    fi
}

# Stop services that might use port 80/443
stop_conflicting_services() {
    print_status "Deteniendo servicios que podrían usar puertos 80/443..."
    
    # Stop nginx if running
    if systemctl is-active --quiet nginx; then
        print_status "Deteniendo Nginx..."
        sudo systemctl stop nginx
    fi
    
    # Stop any Docker containers using these ports
    local containers_using_ports=$(docker ps --format "table {{.Names}}\t{{.Ports}}" | grep -E ":80->|:443->" | cut -f1 || true)
    if [[ -n "$containers_using_ports" ]]; then
        print_status "Deteniendo contenedores que usan puertos 80/443..."
        echo "$containers_using_ports" | while read container; do
            if [[ -n "$container" ]]; then
                docker stop "$container" || true
            fi
        done
    fi
    
    # Wait a moment for ports to be released
    sleep 2
}

# Start services after certificate generation
start_services() {
    print_status "Iniciando servicios..."
    
    # Start nginx if it was running
    if systemctl is-enabled --quiet nginx; then
        print_status "Iniciando Nginx..."
        sudo systemctl start nginx
    fi
}

# Generate self-signed certificate
generate_self_signed() {
    print_status "Generando certificado auto-firmado para $DOMAIN..."
    
    # Generate private key
    openssl genrsa -out "$SSL_DIR/key.pem" 2048
    
    # Generate certificate with SAN for multiple domains
    openssl req -new -x509 -key "$SSL_DIR/key.pem" -out "$SSL_DIR/cert.pem" -days 365 \
        -subj "/C=AR/ST=Cordoba/L=Cordoba/O=Tuchanga/CN=$DOMAIN" \
        -addext "subjectAltName=DNS:$DOMAIN,DNS:www.$DOMAIN,DNS:api.$DOMAIN"
    
    # Set permissions
    chmod 644 "$SSL_DIR/cert.pem"
    chmod 600 "$SSL_DIR/key.pem"
    
    print_success "Certificado auto-firmado generado"
    print_warning "Los navegadores mostrarán advertencias de seguridad para certificados auto-firmados"
}

# Generate Let's Encrypt certificate
generate_letsencrypt() {
    print_status "Generando certificado Let's Encrypt para $DOMAIN..."
    
    # Check if domain resolves to this server
    local server_ip=$(curl -s http://***************/latest/meta-data/public-ipv4 2>/dev/null || curl -s https://ipinfo.io/ip)
    local domain_ip=$(dig +short "$DOMAIN" | tail -n1)
    
    if [[ "$server_ip" != "$domain_ip" ]]; then
        print_warning "El dominio $DOMAIN no apunta a este servidor ($server_ip vs $domain_ip)"
        print_warning "Asegúrate de que el DNS esté configurado correctamente"
        read -p "¿Continuar de todos modos? (y/n): " confirm
        if [[ $confirm != "y" ]]; then
            return 1
        fi
    fi
    
    stop_conflicting_services
    
    # Obtain certificate for all domains
    if sudo certbot certonly --standalone \
        --email "$EMAIL" \
        --agree-tos \
        --no-eff-email \
        --expand \
        -d "$DOMAIN" \
        -d "www.$DOMAIN" \
        -d "api.$DOMAIN"; then
        
        # Copy certificates to our SSL directory
        sudo cp "/etc/letsencrypt/live/$DOMAIN/fullchain.pem" "$SSL_DIR/cert.pem"
        sudo cp "/etc/letsencrypt/live/$DOMAIN/privkey.pem" "$SSL_DIR/key.pem"
        
        # Fix permissions
        sudo chown $USER:$USER "$SSL_DIR/cert.pem" "$SSL_DIR/key.pem"
        chmod 644 "$SSL_DIR/cert.pem"
        chmod 600 "$SSL_DIR/key.pem"
        
        print_success "Certificado Let's Encrypt obtenido exitosamente"
        
        # Setup auto-renewal
        setup_auto_renewal
        
        start_services
        return 0
    else
        print_error "Error al obtener certificado Let's Encrypt"
        start_services
        return 1
    fi
}

# Setup automatic certificate renewal
setup_auto_renewal() {
    print_status "Configurando renovación automática de certificados..."
    
    # Create renewal script
    local renewal_script="/usr/local/bin/tuchanga-cert-renewal.sh"
    sudo tee "$renewal_script" > /dev/null << EOF
#!/bin/bash
# Tuchanga certificate renewal script

# Stop services
systemctl stop nginx || true
docker stop tuchanga-frontend || true

# Renew certificates
certbot renew --quiet

# Copy renewed certificates
if [[ -f "/etc/letsencrypt/live/$DOMAIN/fullchain.pem" ]]; then
    cp "/etc/letsencrypt/live/$DOMAIN/fullchain.pem" "$SSL_DIR/cert.pem"
    cp "/etc/letsencrypt/live/$DOMAIN/privkey.pem" "$SSL_DIR/key.pem"
    chown $USER:$USER "$SSL_DIR/cert.pem" "$SSL_DIR/key.pem"
    chmod 644 "$SSL_DIR/cert.pem"
    chmod 600 "$SSL_DIR/key.pem"
fi

# Restart services
systemctl start nginx || true
cd "$PROJECT_DIR" && docker-compose restart frontend || true
EOF
    
    sudo chmod +x "$renewal_script"
    
    # Add cron job for automatic renewal
    local cron_job="0 3 * * * $renewal_script"
    (crontab -l 2>/dev/null | grep -v "tuchanga-cert-renewal"; echo "$cron_job") | crontab -
    
    print_success "Renovación automática configurada (ejecuta diariamente a las 3:00 AM)"
}

# Choose certificate type
choose_certificate_type() {
    echo ""
    print_status "Selecciona el tipo de certificado SSL:"
    echo "1. Let's Encrypt (Recomendado - Certificado válido y gratuito)"
    echo "2. Auto-firmado (Rápido - Advertencias en navegadores)"
    echo "3. Usar certificados existentes"
    echo ""
    
    read -p "Ingresa tu opción (1-3): " choice
    
    case $choice in
        1)
            if generate_letsencrypt; then
                return 0
            else
                print_warning "Error con Let's Encrypt. ¿Usar certificado auto-firmado como respaldo?"
                read -p "(y/n): " fallback
                if [[ $fallback == "y" ]]; then
                    generate_self_signed
                else
                    return 1
                fi
            fi
            ;;
        2)
            generate_self_signed
            ;;
        3)
            if certificates_exist; then
                print_success "Usando certificados existentes"
            else
                print_error "No se encontraron certificados existentes"
                return 1
            fi
            ;;
        *)
            print_error "Opción inválida"
            return 1
            ;;
    esac
}

# Update environment file with domain
update_environment() {
    print_status "Actualizando archivo de entorno con configuración de dominio..."
    
    cd "$PROJECT_DIR"
    
    if [[ ! -f ".env" ]]; then
        print_error "Archivo .env no encontrado"
        exit 1
    fi
    
    # Update CORS_ORIGIN
    local cors_origin="https://$DOMAIN,https://www.$DOMAIN,https://api.$DOMAIN"
    if grep -q "CORS_ORIGIN=" .env; then
        sed -i "s|CORS_ORIGIN=.*|CORS_ORIGIN=$cors_origin|" .env
    else
        echo "CORS_ORIGIN=$cors_origin" >> .env
    fi
    
    print_success "Archivo de entorno actualizado"
}

# Show certificate information
show_certificate_info() {
    if certificates_exist; then
        print_status "Información del certificado:"
        echo ""
        
        # Certificate details
        local cert_info=$(openssl x509 -in "$SSL_DIR/cert.pem" -text -noout)
        local subject=$(echo "$cert_info" | grep "Subject:" | sed 's/.*Subject: //')
        local issuer=$(echo "$cert_info" | grep "Issuer:" | sed 's/.*Issuer: //')
        local not_after=$(echo "$cert_info" | grep "Not After" | sed 's/.*Not After : //')
        
        echo "📋 Sujeto: $subject"
        echo "🏢 Emisor: $issuer"
        echo "📅 Expira: $not_after"
        
        # Calculate days until expiry
        local expiry_epoch=$(date -d "$not_after" +%s)
        local current_epoch=$(date +%s)
        local days_until_expiry=$(( (expiry_epoch - current_epoch) / 86400 ))
        
        if [[ $days_until_expiry -gt 30 ]]; then
            echo "✅ Estado: Válido ($days_until_expiry días restantes)"
        elif [[ $days_until_expiry -gt 0 ]]; then
            echo "⚠️  Estado: Expira pronto ($days_until_expiry días restantes)"
        else
            echo "❌ Estado: Expirado"
        fi
        
        echo ""
        echo "📁 Ubicación de archivos:"
        echo "   Certificado: $SSL_DIR/cert.pem"
        echo "   Clave privada: $SSL_DIR/key.pem"
    else
        echo "❌ No se encontraron certificados"
    fi
}

# Main function
main() {
    print_status "Configurando certificados SSL para $DOMAIN..."
    echo ""
    
    check_prerequisites
    create_ssl_directory
    
    # Check if valid certificates already exist
    if validate_certificates; then
        print_success "Certificados válidos ya existen"
        read -p "¿Regenerar certificados? (y/n): " regenerate
        if [[ $regenerate != "y" ]]; then
            show_certificate_info
            return 0
        fi
    fi
    
    choose_certificate_type
    update_environment
    
    echo ""
    show_certificate_info
    
    print_success "¡Certificados SSL configurados exitosamente!"
    echo ""
    print_status "Tu aplicación estará disponible en:"
    echo "- https://$DOMAIN"
    echo "- https://www.$DOMAIN"
    echo "- https://api.$DOMAIN (API)"
    echo ""
}

# Run main function
main "$@"
