import { NestFactory } from '@nestjs/core';
import { ValidationPipe } from '@nestjs/common';
import { SwaggerModule, DocumentBuilder } from '@nestjs/swagger';
import { AppModule } from './app.module';
import { NestExpressApplication } from '@nestjs/platform-express';
import { join } from 'path';
// Initialize Firebase Admin SDK
import './config/firebase.config';

async function bootstrap() {
  const app = await NestFactory.create<NestExpressApplication>(AppModule);

  // Serve static files for uploaded images
  app.useStaticAssets(join(__dirname, '..', 'uploads'), {
    prefix: '/uploads/',
  });

  // Enable CORS for frontend communication
  app.enableCors({
    origin: function (origin, callback) {
      // Allow requests with no origin (like mobile apps or curl requests)
      if (!origin) return callback(null, true);

      // Define allowed origins
      const allowedOrigins = [
        // Development origins
        'http://localhost:5173',
        'http://localhost:5174',
        'http://localhost:3000',
        'http://localhost:80',
        'http://localhost',

        // Production domain
        'http://tuchanga.com',
        'https://tuchanga.com',
        'http://www.tuchanga.com',
        'https://www.tuchanga.com',
        'https://api.tuchanga.com',

        // EC2 instances (dynamic)
        'http://ec2-54-233-23-129.sa-east-1.compute.amazonaws.com',
        'http://*************',
        'http://**************',
        'http://ec2-18-229-140-118.sa-east-1.compute.amazonaws.com',
        'https://ec2-18-231-110-178.sa-east-1.compute.amazonaws.com',
        'https://**************'
      ];

      // Check if origin is allowed or if it's an EC2 instance
      if (allowedOrigins.includes(origin) ||
          origin.includes('ec2-') ||
          origin.includes('.compute.amazonaws.com') ||
          /^https?:\/\/\d+\.\d+\.\d+\.\d+(:\d+)?$/.test(origin)) {
        callback(null, true);
      } else {
        console.log('CORS blocked origin:', origin);
        callback(new Error('Not allowed by CORS'));
      }
    },
    credentials: true,
    methods: ['GET', 'POST', 'PUT', 'PATCH', 'DELETE', 'OPTIONS'],
    allowedHeaders: ['Content-Type', 'Authorization', 'Accept'],
  });

  // Global validation pipe
  app.useGlobalPipes(
    new ValidationPipe({
      whitelist: true,
      forbidNonWhitelisted: true,
      transform: true,
    }),
  );

  // Swagger API documentation
  const config = new DocumentBuilder()
    .setTitle('Job Platform API')
    .setDescription('A comprehensive job platform API for connecting employers and employees')
    .setVersion('1.0')
    .addBearerAuth()
    .build();

  const document = SwaggerModule.createDocument(app, config);
  SwaggerModule.setup('api/docs', app, document);

  const port = process.env.PORT || 3000;
  const host = process.env.HOST || '0.0.0.0'; // Bind to all interfaces for Docker
  await app.listen(port, host);

  console.log(`🚀 Job Platform API is running on: http://${host}:${port}`);
  console.log(`📚 API Documentation available at: http://${host}:${port}/api/docs`);
  console.log(`🔥 Firebase Admin SDK ready for production authentication`);
}
bootstrap();
