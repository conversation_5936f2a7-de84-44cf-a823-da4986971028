#!/bin/bash

# Tuchanga EC2 Ubuntu 24 LTS Complete Deployment Script
# This script automates the complete setup of Tuchanga application on EC2
# Author: Augment Agent
# Version: 1.0

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Script directory
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "$SCRIPT_DIR/../.." && pwd)"

# Configuration variables
DOMAIN=""
EMAIL=""
DB_PASSWORD=""
JWT_SECRET=""

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_header() {
    echo -e "${PURPLE}================================${NC}"
    echo -e "${PURPLE}$1${NC}"
    echo -e "${PURPLE}================================${NC}"
}

# Function to check if command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Function to check if service is running
service_running() {
    systemctl is-active --quiet "$1" 2>/dev/null
}

# Function to check if port is in use
port_in_use() {
    netstat -tuln | grep -q ":$1 "
}

# Function to generate secure password
generate_password() {
    openssl rand -base64 32 | tr -d "=+/" | cut -c1-25
}

# Function to collect user input
collect_user_input() {
    print_header "CONFIGURACIÓN INICIAL"

    echo "Este script configurará automáticamente tu aplicación Tuchanga en EC2 Ubuntu 24 LTS"
    echo "El repositorio ya está clonado, solo necesitamos configurar el despliegue:"
    echo ""

    # Domain
    while [[ -z "$DOMAIN" ]]; do
        read -p "🌐 Ingresa tu dominio (ej: midominio.com): " DOMAIN
        if [[ -z "$DOMAIN" ]]; then
            print_error "El dominio es obligatorio"
        fi
    done

    # Email
    while [[ -z "$EMAIL" ]]; do
        read -p "📧 Ingresa tu email para certificados SSL: " EMAIL
        if [[ -z "$EMAIL" ]]; then
            print_error "El email es obligatorio"
        fi
    done

    # Database password
    DB_PASSWORD=$(generate_password)
    read -p "🔐 Contraseña de la base de datos [generada automáticamente]: " input_db_pass
    if [[ -n "$input_db_pass" ]]; then
        DB_PASSWORD="$input_db_pass"
    fi

    # JWT Secret
    JWT_SECRET=$(generate_password)
    read -p "🔑 JWT Secret [generado automáticamente]: " input_jwt
    if [[ -n "$input_jwt" ]]; then
        JWT_SECRET="$input_jwt"
    fi

    echo ""
    print_success "Configuración recopilada exitosamente"
    echo "Dominio: $DOMAIN"
    echo "Email: $EMAIL"
    echo "Directorio del proyecto: $PROJECT_ROOT"
    echo ""
}

# Function to show menu
show_menu() {
    clear
    print_header "TUCHANGA EC2 DEPLOYMENT MANAGER"
    echo ""
    echo "Selecciona una opción:"
    echo ""
    echo "1. 🚀 Instalación completa desde cero"
    echo "2. ✅ Verificar requisitos del sistema"
    echo "3. 🔧 Instalar dependencias del sistema"
    echo "4. 🗄️  Configurar base de datos"
    echo "5. ⚙️  Configurar aplicación"
    echo "6. 🔐 Configurar certificados SSL"
    echo "7. 🌐 Configurar Nginx"
    echo "8. ▶️  Iniciar aplicación"
    echo "9. 📊 Ver estado de servicios"
    echo "10. 🔄 Reiniciar servicios"
    echo "11. 🛑 Detener servicios"
    echo "12. 📋 Ver logs"
    echo "0. ❌ Salir"
    echo ""
}

# Function to wait for user input
wait_for_input() {
    echo ""
    read -p "Presiona Enter para continuar..."
}

# Main execution
main() {
    # Check if running as root
    if [[ $EUID -eq 0 ]]; then
        print_error "No ejecutes este script como root. Usa un usuario con sudo."
        exit 1
    fi
    
    # Check if running on Ubuntu
    if [[ ! -f /etc/os-release ]] || ! grep -q "Ubuntu" /etc/os-release; then
        print_error "Este script está diseñado para Ubuntu 24 LTS"
        exit 1
    fi
    
    while true; do
        show_menu
        read -p "Ingresa tu opción [1-12, 0 para salir]: " choice

        case $choice in
            1)
                collect_user_input
                print_header "VERIFICACIÓN PRE-INSTALACIÓN"

                # Run pre-install check but don't stop on warnings
                if "$SCRIPT_DIR/pre-install-check.sh" "$DOMAIN"; then
                    print_success "Verificación completada - continuando con la instalación"
                    auto_continue="y"
                else
                    print_warning "Se encontraron algunos problemas en la verificación"
                    read -p "¿Continuar con la instalación de todos modos? (y/n): " auto_continue
                fi

                if [[ $auto_continue == "y" ]]; then
                    print_header "INSTALACIÓN COMPLETA"
                    print_status "Iniciando instalación automática de Tuchanga..."
                    echo ""

                    # Execute installation steps
                    if "$SCRIPT_DIR/install-dependencies.sh"; then
                        print_success "✅ Dependencias instaladas"
                    else
                        print_error "❌ Error instalando dependencias"
                        wait_for_input
                        continue
                    fi



                    if "$SCRIPT_DIR/setup-database.sh" "$DB_PASSWORD"; then
                        print_success "✅ Base de datos configurada"
                    else
                        print_error "❌ Error configurando base de datos"
                        wait_for_input
                        continue
                    fi

                    if "$SCRIPT_DIR/setup-application.sh"; then
                        print_success "✅ Aplicación configurada"
                    else
                        print_error "❌ Error configurando aplicación"
                        wait_for_input
                        continue
                    fi

                    if "$SCRIPT_DIR/setup-ssl.sh" "$DOMAIN" "$EMAIL"; then
                        print_success "✅ SSL configurado"
                    else
                        print_error "❌ Error configurando SSL"
                        wait_for_input
                        continue
                    fi

                    if "$SCRIPT_DIR/setup-nginx.sh" "$DOMAIN"; then
                        print_success "✅ Nginx configurado"
                    else
                        print_error "❌ Error configurando Nginx"
                        wait_for_input
                        continue
                    fi

                    if "$SCRIPT_DIR/start-application.sh" "$DOMAIN" "$DB_PASSWORD" "$JWT_SECRET"; then
                        print_success "✅ Aplicación iniciada"
                        echo ""
                        print_success "🎉 ¡INSTALACIÓN COMPLETA FINALIZADA EXITOSAMENTE!"
                        echo ""
                        print_status "Tu aplicación Tuchanga está disponible en:"
                        echo "🌐 https://$DOMAIN"
                        echo "🌐 https://www.$DOMAIN"
                        echo "🔗 https://api.$DOMAIN"
                        echo ""
                        print_status "Comandos útiles:"
                        echo "- Ver estado: ./check-status.sh"
                        echo "- Ver logs: ./show-logs.sh"
                        echo "- Reiniciar: ./restart-services.sh"
                    else
                        print_error "❌ Error iniciando aplicación"
                    fi
                else
                    print_status "Instalación cancelada por el usuario"
                fi
                wait_for_input
                ;;
            2)
                if [[ -z "$DOMAIN" ]]; then
                    read -p "🌐 Dominio (opcional para verificación DNS): " DOMAIN
                fi
                print_header "VERIFICANDO REQUISITOS"
                "$SCRIPT_DIR/pre-install-check.sh" "$DOMAIN"
                wait_for_input
                ;;
            3)
                print_header "INSTALANDO DEPENDENCIAS"
                "$SCRIPT_DIR/install-dependencies.sh"
                wait_for_input
                ;;
            4)
                if [[ -z "$DB_PASSWORD" ]]; then
                    DB_PASSWORD=$(generate_password)
                    read -p "🔐 Contraseña de la base de datos [$DB_PASSWORD]: " input_pass
                    if [[ -n "$input_pass" ]]; then
                        DB_PASSWORD="$input_pass"
                    fi
                fi
                print_header "CONFIGURANDO BASE DE DATOS"
                "$SCRIPT_DIR/setup-database.sh" "$DB_PASSWORD"
                wait_for_input
                ;;
            5)
                print_header "CONFIGURANDO APLICACIÓN"
                "$SCRIPT_DIR/setup-application.sh"
                wait_for_input
                ;;
            6)
                if [[ -z "$DOMAIN" ]] || [[ -z "$EMAIL" ]]; then
                    read -p "🌐 Dominio: " DOMAIN
                    read -p "📧 Email: " EMAIL
                fi
                print_header "CONFIGURANDO SSL"
                "$SCRIPT_DIR/setup-ssl.sh" "$DOMAIN" "$EMAIL"
                wait_for_input
                ;;
            7)
                if [[ -z "$DOMAIN" ]]; then
                    read -p "🌐 Dominio: " DOMAIN
                fi
                print_header "CONFIGURANDO NGINX"
                "$SCRIPT_DIR/setup-nginx.sh" "$DOMAIN"
                wait_for_input
                ;;
            8)
                if [[ -z "$DOMAIN" ]] || [[ -z "$DB_PASSWORD" ]] || [[ -z "$JWT_SECRET" ]]; then
                    read -p "🌐 Dominio: " DOMAIN
                    read -p "🔐 Contraseña DB: " DB_PASSWORD
                    read -p "🔑 JWT Secret: " JWT_SECRET
                fi
                print_header "INICIANDO APLICACIÓN"
                "$SCRIPT_DIR/start-application.sh" "$DOMAIN" "$DB_PASSWORD" "$JWT_SECRET"
                wait_for_input
                ;;
            9)
                print_header "ESTADO DE SERVICIOS"
                "$SCRIPT_DIR/check-status.sh"
                wait_for_input
                ;;
            10)
                print_header "REINICIANDO SERVICIOS"
                "$SCRIPT_DIR/restart-services.sh"
                wait_for_input
                ;;
            11)
                print_header "DETENIENDO SERVICIOS"
                "$SCRIPT_DIR/stop-services.sh"
                wait_for_input
                ;;
            12)
                print_header "LOGS DE SERVICIOS"
                "$SCRIPT_DIR/show-logs.sh"
                wait_for_input
                ;;
            0)
                print_success "¡Hasta luego!"
                exit 0
                ;;
            *)
                print_error "Opción inválida. Intenta de nuevo."
                wait_for_input
                ;;
        esac
    done
}

# Run main function
main "$@"
