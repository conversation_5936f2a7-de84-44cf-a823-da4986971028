#!/bin/bash

# Setup script specifically for Amazon Linux EC2 instances
# Installs all dependencies and configures HTTPS for Tuchanga Job Platform

set -e

echo "🐧 Amazon Linux Setup for Tuchanga Job Platform"
echo "==============================================="

# Check if running on Amazon Linux
if [[ -f /etc/os-release ]]; then
    source /etc/os-release
    if [[ "$ID" != "amzn" && "$ID" != "amazon" ]]; then
        echo "⚠️  This script is designed for Amazon Linux"
        echo "Current OS: $PRETTY_NAME"
        read -p "Continue anyway? (y/n): " confirm
        if [[ $confirm != "y" ]]; then
            echo "❌ Aborted"
            exit 1
        fi
    fi
else
    echo "⚠️  Cannot detect OS. Assuming Amazon Linux..."
fi

echo "📦 Installing system dependencies..."

# Update system
sudo yum update -y

# Install essential packages
sudo yum groupinstall -y "Development Tools"
sudo yum install -y \
    git \
    curl \
    wget \
    openssl \
    python3 \
    python3-pip \
    htop \
    nano \
    vim

# Install Docker if not present
if ! command -v docker &> /dev/null; then
    echo "🐳 Installing Docker..."
    sudo yum install -y docker
    sudo systemctl start docker
    sudo systemctl enable docker
    sudo usermod -a -G docker ec2-user
    echo "✅ Docker installed. You may need to log out and back in for group changes to take effect."
else
    echo "✅ Docker already installed"
fi

# Install Docker Compose if not present
if ! command -v docker-compose &> /dev/null; then
    echo "🐳 Installing Docker Compose..."
    sudo curl -L "https://github.com/docker/compose/releases/latest/download/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
    sudo chmod +x /usr/local/bin/docker-compose
    
    # Create symlink for easier access
    sudo ln -sf /usr/local/bin/docker-compose /usr/bin/docker-compose
    echo "✅ Docker Compose installed"
else
    echo "✅ Docker Compose already installed"
fi

# Install Node.js (for development/debugging)
if ! command -v node &> /dev/null; then
    echo "📦 Installing Node.js..."
    curl -fsSL https://rpm.nodesource.com/setup_18.x | sudo bash -
    sudo yum install -y nodejs
    echo "✅ Node.js installed"
else
    echo "✅ Node.js already installed"
fi

# Install certbot for Let's Encrypt
if ! command -v certbot &> /dev/null; then
    echo "🔐 Installing Certbot..."
    sudo pip3 install certbot
    echo "✅ Certbot installed"
else
    echo "✅ Certbot already installed"
fi

echo ""
echo "🔧 System setup complete!"
echo ""

# Check if we're in the project directory
if [[ ! -f "docker-compose.yml" ]]; then
    echo "📁 Project files not found in current directory"
    echo "Please navigate to your project directory and run:"
    echo "   cd /path/to/tuchanga"
    echo "   ./setup-amazon-linux.sh"
    exit 1
fi

echo "🔐 Setting up HTTPS certificates..."

# Configuration
DOMAIN="ec2-18-231-110-178.sa-east-1.compute.amazonaws.com"
SSL_DIR="./ssl"

# Create SSL directory
mkdir -p $SSL_DIR

echo ""
echo "📋 Choose SSL certificate option:"
echo "1. Self-signed certificate (Quick setup, browser warnings)"
echo "2. Let's Encrypt certificate (Recommended, requires domain access)"
echo "3. Skip SSL setup (configure manually later)"

read -p "Enter your choice (1-3): " choice

case $choice in
    1)
        echo "🔧 Generating self-signed certificate..."
        
        # Generate private key
        openssl genrsa -out $SSL_DIR/key.pem 2048
        
        # Generate certificate
        openssl req -new -x509 -key $SSL_DIR/key.pem -out $SSL_DIR/cert.pem -days 365 \
            -subj "/C=AR/ST=Cordoba/L=Cordoba/O=Tuchanga/CN=$DOMAIN"
        
        # Set permissions
        chmod 644 $SSL_DIR/cert.pem
        chmod 600 $SSL_DIR/key.pem
        
        echo "✅ Self-signed certificate generated!"
        echo "⚠️  Note: Browsers will show security warnings for self-signed certificates"
        ;;
        
    2)
        echo "🔧 Setting up Let's Encrypt certificate..."
        echo "⚠️  Make sure your domain $DOMAIN points to this server"
        echo "⚠️  Ports 80 and 443 must be accessible from the internet"
        
        # Check security groups
        echo ""
        echo "🔍 Checking if ports are accessible..."
        if timeout 5 bash -c "</dev/tcp/***************/80" 2>/dev/null; then
            echo "✅ Port 80 appears to be accessible"
        else
            echo "⚠️  Cannot verify port 80 accessibility"
        fi
        
        read -p "Continue with Let's Encrypt setup? (y/n): " confirm
        if [[ $confirm != "y" ]]; then
            echo "❌ Skipping Let's Encrypt setup"
            choice=3
        else
            # Stop any running containers to free port 80
            echo "🛑 Stopping containers to free port 80..."
            docker-compose down || true
            
            # Obtain certificate
            EMAIL="<EMAIL>"  # Default email
            read -p "Enter email for Let's Encrypt notifications [$EMAIL]: " user_email
            if [[ -n "$user_email" ]]; then
                EMAIL="$user_email"
            fi
            
            sudo certbot certonly --standalone \
                --email $EMAIL \
                --agree-tos \
                --no-eff-email \
                -d $DOMAIN
            
            # Copy certificates to our SSL directory
            sudo cp /etc/letsencrypt/live/$DOMAIN/fullchain.pem $SSL_DIR/cert.pem
            sudo cp /etc/letsencrypt/live/$DOMAIN/privkey.pem $SSL_DIR/key.pem
            
            # Fix permissions
            sudo chown $(whoami):$(whoami) $SSL_DIR/cert.pem $SSL_DIR/key.pem
            
            echo "✅ Let's Encrypt certificate obtained!"
            
            # Setup auto-renewal
            echo "🔄 Setting up certificate auto-renewal..."
            (sudo crontab -l 2>/dev/null; echo "0 12 * * * /usr/local/bin/certbot renew --quiet && docker-compose restart frontend") | sudo crontab -
            echo "✅ Auto-renewal configured"
        fi
        ;;
        
    3)
        echo "⏭️  Skipping SSL setup"
        echo "You can run ./generate-ssl.sh later to generate certificates"
        ;;
        
    *)
        echo "❌ Invalid choice, skipping SSL setup"
        choice=3
        ;;
esac

# Start the application
if [[ $choice != "3" ]]; then
    echo ""
    echo "🚀 Starting application with HTTPS..."
    docker-compose up -d --build
    
    # Wait for containers to be ready
    echo "⏳ Waiting for containers to start..."
    sleep 15
    
    # Check container status
    echo "📊 Container status:"
    docker-compose ps
    
    echo ""
    echo "🎉 Setup complete!"
    echo ""
    echo "🌐 Your application is available at:"
    echo "   https://$DOMAIN"
    echo ""
else
    echo ""
    echo "🎉 System setup complete!"
    echo ""
    echo "📋 Next steps:"
    echo "   1. Generate SSL certificates: ./generate-ssl.sh"
    echo "   2. Start application: docker-compose up -d --build"
    echo "   3. Visit: https://$DOMAIN"
    echo ""
fi

echo "📋 Useful commands:"
echo "   docker-compose ps              # Check container status"
echo "   docker-compose logs            # View all logs"
echo "   docker-compose logs frontend   # View frontend logs"
echo "   docker-compose logs backend    # View backend logs"
echo "   sudo systemctl status docker   # Check Docker service"
echo ""
echo "✅ Amazon Linux setup complete!"
