# 🚀 Tuchanga Deployment Troubleshooting Guide

## 🎯 Nuevo: Manager Interactivo

**¡Usa el nuevo manager interactivo para todo!**

```bash
# Hacer scripts ejecutables
chmod +x *.sh

# Ejecutar el manager principal
./tuchanga-manager.sh
```

El manager te permite:
- ✅ Iniciar containers paso a paso (evita que se cuelgue la instancia)
- ✅ Diagnosticar problemas específicos
- ✅ Parar containers de manera controlada
- ✅ Monitorear recursos y logs
- ✅ Aplicar fixes automáticos

## 📋 Diagnóstico Manual

Si prefieres ejecutar comandos individuales:

```bash
# 1. Hacer los scripts ejecutables
chmod +x *.sh

# 2. Ejecutar diagnóstico completo
./diagnose-deployment.sh

# 3. Verificar configuración de API específicamente
./check-api-config.sh
```

## 🔧 Inicio Paso a Paso (Recomendado)

Para evitar que tu instancia EC2 se cuelgue:

```bash
# Iniciar containers uno por uno
./start-containers-step-by-step.sh

# Parar containers de manera controlada
./stop-containers-gracefully.sh
```

## 🔍 Problemas Comunes y Soluciones

### 1. **Containers no están corriendo**
```bash
# Verificar estado
docker ps

# Si no están corriendo, iniciar
docker-compose up -d

# Ver logs si hay errores
docker-compose logs -f
```

### 2. **Puertos no accesibles desde internet**

**AWS Security Group:**
- Asegúrate de que tu Security Group permita:
  - HTTP (puerto 80) desde 0.0.0.0/0
  - HTTPS (puerto 443) desde 0.0.0.0/0
  - Custom TCP (puerto 3000) desde 0.0.0.0/0

**Firewall local:**
```bash
# Para UFW (Ubuntu)
sudo ufw allow 80/tcp
sudo ufw allow 443/tcp
sudo ufw allow 3000/tcp

# Para firewalld (Amazon Linux)
sudo firewall-cmd --permanent --add-port=80/tcp
sudo firewall-cmd --permanent --add-port=443/tcp
sudo firewall-cmd --permanent --add-port=3000/tcp
sudo firewall-cmd --reload
```

### 3. **SSL/HTTPS no funciona**

```bash
# Generar certificados self-signed
./setup-https.sh

# O manualmente:
mkdir -p ssl
openssl genrsa -out ssl/key.pem 2048
openssl req -new -key ssl/key.pem -out ssl/cert.csr -subj "/C=US/ST=State/L=City/O=Tuchanga/CN=tuchanga.com"
openssl x509 -req -days 365 -in ssl/cert.csr -signkey ssl/key.pem -out ssl/cert.pem
rm ssl/cert.csr
chmod 644 ssl/cert.pem
chmod 600 ssl/key.pem
```

### 4. **DNS no resuelve a tu servidor**

Verifica que tu dominio apunte a la IP correcta:
```bash
# Obtener tu IP pública
curl http://checkip.amazonaws.com/

# Verificar DNS
nslookup tuchanga.com
```

Si no coinciden, actualiza el registro A en tu proveedor de dominio.

### 5. **API no responde**

```bash
# Probar endpoints directamente
curl http://localhost:3000/health
curl http://localhost/api/health
curl https://localhost/api/health -k

# Si el backend no responde, revisar logs
docker logs job-platform-backend
```

## 🌐 URLs de Prueba

Una vez que todo esté funcionando, deberías poder acceder a:

```
# Con IP pública (reemplaza con tu IP)
http://TU_IP_PUBLICA
https://TU_IP_PUBLICA (certificado self-signed)
http://TU_IP_PUBLICA:3000/health

# Con dominio (si está configurado)
http://tuchanga.com
https://tuchanga.com
https://api.tuchanga.com/health
```

## 🔄 Reinicio Completo

Si nada funciona, reinicio completo:

```bash
# Parar todo
docker-compose down

# Limpiar recursos
docker system prune -f

# Reconstruir y iniciar
docker-compose build --no-cache
docker-compose up -d

# Esperar y verificar
sleep 30
docker ps
```

## 📊 Verificación Final

Ejecuta estos comandos para verificar que todo funciona:

```bash
# 1. Verificar containers
docker ps --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}"

# 2. Verificar puertos
netstat -tuln | grep -E ":(80|443|3000|5432) "

# 3. Probar API
curl -s http://localhost:3000/health | jq .

# 4. Probar frontend
curl -s http://localhost/ | head -10

# 5. Probar proxy
curl -s http://localhost/api/health | jq .
```

## 🆘 Si Aún No Funciona

1. **Ejecuta el diagnóstico completo:**
   ```bash
   ./diagnose-deployment.sh > diagnosis.txt
   ```

2. **Comparte el output del diagnóstico** para obtener ayuda específica.

3. **Verifica logs detallados:**
   ```bash
   docker-compose logs > full-logs.txt
   ```

## 📝 Notas Importantes

- **Certificados Self-signed:** Los navegadores mostrarán advertencias de seguridad
- **Firewall:** Asegúrate de que tanto AWS Security Group como el firewall local permitan el tráfico
- **DNS:** Puede tomar hasta 48 horas para que los cambios de DNS se propaguen
- **Recursos:** Los containers están limitados al 70% de CPU para evitar que la instancia EC2 se cuelgue

## 🔧 Comandos Útiles

```bash
# Ver logs en tiempo real
docker-compose logs -f

# Reiniciar un servicio específico
docker-compose restart backend

# Entrar a un container
docker exec -it job-platform-backend bash

# Ver uso de recursos
docker stats

# Verificar red de Docker
docker network ls
docker network inspect job-platform_job-platform-network
```
