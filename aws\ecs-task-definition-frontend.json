{"family": "tuchanga-frontend", "networkMode": "awsvpc", "requiresCompatibilities": ["FARGATE"], "cpu": "256", "memory": "512", "executionRoleArn": "arn:aws:iam::ACCOUNT_ID:role/ecsTaskExecutionRole", "containerDefinitions": [{"name": "tuchanga-frontend", "image": "ACCOUNT_ID.dkr.ecr.sa-east-1.amazonaws.com/tuchanga-frontend:latest", "portMappings": [{"containerPort": 80, "protocol": "tcp"}], "essential": true, "environment": [{"name": "BACKEND_URL", "value": "http://tuchanga-backend-alb-internal-xxxxx.sa-east-1.elb.amazonaws.com"}], "logConfiguration": {"logDriver": "awslogs", "options": {"awslogs-group": "/ecs/tuchanga-frontend", "awslogs-region": "sa-east-1", "awslogs-stream-prefix": "ecs"}}, "healthCheck": {"command": ["CMD-SHELL", "curl -f http://localhost:80/health || exit 1"], "interval": 30, "timeout": 5, "retries": 3, "startPeriod": 30}}]}