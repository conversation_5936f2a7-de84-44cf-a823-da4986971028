#!/bin/bash

# Start Tuchanga application natively
# This script starts all services and deploys the application without Docker

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to check if command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Configuration
DOMAIN="$1"
DB_PASSWORD="$2"
JWT_SECRET="$3"
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_DIR="$(cd "$SCRIPT_DIR/../.." && pwd)"
WEB_DIR="/var/www/tuchanga"

print_status "Directorio del proyecto detectado: $PROJECT_DIR"

# Validate inputs
if [[ -z "$DOMAIN" ]] || [[ -z "$DB_PASSWORD" ]] || [[ -z "$JWT_SECRET" ]]; then
    print_error "Parámetros requeridos faltantes"
    echo "Uso: $0 <domain> <db_password> <jwt_secret>"
    echo "Ejemplo: $0 midominio.com mypassword mysecret"
    exit 1
fi

# Check prerequisites
check_prerequisites() {
    if ! command_exists psql; then
        print_error "PostgreSQL no está instalado"
        exit 1
    fi

    if ! systemctl is-active --quiet postgresql; then
        print_error "PostgreSQL no está ejecutándose"
        exit 1
    fi

    if ! command_exists pm2; then
        print_error "PM2 no está instalado"
        exit 1
    fi

    if [[ ! -d "$PROJECT_DIR" ]]; then
        print_error "Directorio del proyecto no encontrado: $PROJECT_DIR"
        exit 1
    fi

    if ! systemctl is-active --quiet nginx; then
        print_error "Nginx no está ejecutándose"
        exit 1
    fi
}

# Update environment configuration
update_environment() {
    print_status "Actualizando configuración de entorno..."
    
    cd "$PROJECT_DIR"
    
    if [[ ! -f ".env" ]]; then
        print_error "Archivo .env no encontrado"
        exit 1
    fi
    
    # Update database configuration
    sed -i "s/DB_HOST=.*/DB_HOST=localhost/" .env
    sed -i "s/DB_PORT=.*/DB_PORT=5432/" .env
    sed -i "s/DB_USERNAME=.*/DB_USERNAME=tuchanga_user/" .env
    sed -i "s/DB_DATABASE=.*/DB_DATABASE=tuchanga/" .env
    sed -i "s/DB_PASSWORD=.*/DB_PASSWORD=$DB_PASSWORD/" .env
    sed -i "s|DATABASE_URL=.*|DATABASE_URL=postgresql://tuchanga_user:$DB_PASSWORD@localhost:5432/tuchanga|" .env
    
    # Update JWT secret
    sed -i "s/JWT_SECRET=.*/JWT_SECRET=$JWT_SECRET/" .env
    
    # Update CORS origin
    local cors_origin="https://$DOMAIN,https://www.$DOMAIN,https://api.$DOMAIN"
    sed -i "s|CORS_ORIGIN=.*|CORS_ORIGIN=$cors_origin|" .env
    
    # Ensure production environment
    sed -i "s/NODE_ENV=.*/NODE_ENV=production/" .env
    
    print_success "Configuración de entorno actualizada"
}

# Stop existing processes
stop_existing_processes() {
    print_status "Deteniendo procesos existentes..."

    # Stop PM2 processes
    pm2 stop all 2>/dev/null || true
    pm2 delete all 2>/dev/null || true

    print_success "Procesos existentes detenidos"
}

# Check database connection
check_database() {
    print_status "Verificando conexión a la base de datos..."

    if psql -h localhost -p 5432 -U tuchanga_user -d tuchanga -c "SELECT 1;" >/dev/null 2>&1; then
        print_success "Conexión a la base de datos exitosa"
    else
        print_error "No se puede conectar a la base de datos"
        print_status "Verifica que la base de datos esté configurada correctamente"
        exit 1
    fi
}

# Start backend with PM2
start_backend() {
    print_status "Iniciando backend con PM2..."

    cd "$PROJECT_DIR"

    # Use the setup-application script
    if "$SCRIPT_DIR/setup-application.sh"; then
        print_success "Backend iniciado con PM2"
    else
        print_error "Error al iniciar backend"
        exit 1
    fi
}

# Frontend is already built and deployed by setup-application.sh
check_frontend() {
    print_status "Verificando frontend..."

    if [[ -d "$WEB_DIR" ]] && [[ -f "$WEB_DIR/index.html" ]]; then
        print_success "Frontend desplegado correctamente"
    else
        print_warning "Frontend no encontrado, será desplegado por setup-application.sh"
    fi
}

# Start monitoring and health checks
start_monitoring() {
    print_status "Configurando monitoreo y health checks..."
    
    # Create health check script
    local health_script="/usr/local/bin/tuchanga-health-check.sh"
    sudo tee "$health_script" > /dev/null << EOF
#!/bin/bash
# Tuchanga health check script

echo "=== Tuchanga Health Check - \$(date) ==="

# Check Nginx
if systemctl is-active --quiet nginx; then
    echo "✅ Nginx: Running"
else
    echo "❌ Nginx: Not running"
    systemctl restart nginx
fi

# Check Database
if docker exec tuchanga-postgres pg_isready -U postgres -d tuchanga >/dev/null 2>&1; then
    echo "✅ Database: Running"
else
    echo "❌ Database: Not running"
    cd "$PROJECT_DIR" && docker-compose restart postgres
fi

# Check Backend
if curl -f http://localhost:3000/health >/dev/null 2>&1; then
    echo "✅ Backend: Running"
else
    echo "❌ Backend: Not running"
    cd "$PROJECT_DIR" && docker-compose restart backend
fi

# Check HTTPS
if curl -f -k https://$DOMAIN >/dev/null 2>&1; then
    echo "✅ HTTPS: Working"
else
    echo "❌ HTTPS: Not working"
fi

echo "=== End Health Check ==="
echo ""
EOF
    
    sudo chmod +x "$health_script"
    
    # Add cron job for health checks (every 5 minutes)
    local cron_job="*/5 * * * * $health_script >> /var/log/tuchanga-health.log 2>&1"
    (crontab -l 2>/dev/null | grep -v "tuchanga-health-check"; echo "$cron_job") | crontab -
    
    print_success "Monitoreo configurado"
}

# Create backup script
create_backup_script() {
    print_status "Creando script de backup automático..."
    
    local backup_script="/usr/local/bin/tuchanga-backup.sh"
    sudo tee "$backup_script" > /dev/null << EOF
#!/bin/bash
# Tuchanga automatic backup script

BACKUP_DIR="/opt/tuchanga-backups"
DATE=\$(date +%Y%m%d-%H%M%S)

# Create backup directory
mkdir -p "\$BACKUP_DIR"

# Backup database
echo "Creating database backup..."
docker exec tuchanga-postgres pg_dump -U postgres tuchanga > "\$BACKUP_DIR/database-\$DATE.sql"

# Backup project files
echo "Creating project backup..."
tar -czf "\$BACKUP_DIR/project-\$DATE.tar.gz" -C /opt tuchanga --exclude=node_modules --exclude=dist

# Keep only last 7 backups
cd "\$BACKUP_DIR"
ls -t database-*.sql | tail -n +8 | xargs -r rm
ls -t project-*.tar.gz | tail -n +8 | xargs -r rm

echo "Backup completed: \$DATE"
EOF
    
    sudo chmod +x "$backup_script"
    
    # Add daily backup cron job
    local backup_cron="0 2 * * * $backup_script >> /var/log/tuchanga-backup.log 2>&1"
    (crontab -l 2>/dev/null | grep -v "tuchanga-backup"; echo "$backup_cron") | crontab -
    
    print_success "Backup automático configurado (diario a las 2:00 AM)"
}

# Show application status
show_application_status() {
    print_status "Estado de la aplicación:"
    echo ""
    
    # PM2 status
    echo "📦 Procesos PM2:"
    pm2 list | grep tuchanga || echo "   No hay procesos de Tuchanga ejecutándose"
    
    echo ""
    
    # Service status
    echo "🔧 Servicios:"
    if systemctl is-active --quiet nginx; then
        echo "   ✅ Nginx: Activo"
    else
        echo "   ❌ Nginx: Inactivo"
    fi
    
    if systemctl is-active --quiet postgresql; then
        echo "   ✅ PostgreSQL: Activo"
    else
        echo "   ❌ PostgreSQL: Inactivo"
    fi

    if pm2 list | grep -q "tuchanga-backend.*online"; then
        echo "   ✅ Backend (PM2): Activo"
    else
        echo "   ❌ Backend (PM2): Inactivo"
    fi
    
    echo ""
    
    # URLs
    echo "🌐 URLs disponibles:"
    echo "   Frontend: https://$DOMAIN"
    echo "   Frontend (www): https://www.$DOMAIN"
    echo "   API: https://api.$DOMAIN"
    echo "   API (proxy): https://$DOMAIN/api/"
    
    echo ""
    
    # Resource usage
    echo "📊 Uso de recursos:"
    pm2 monit --no-daemon 2>/dev/null | head -10 || echo "   Ejecuta 'pm2 monit' para ver recursos"
    
    echo ""
}

# Main function
main() {
    print_status "Iniciando aplicación Tuchanga..."
    echo ""
    
    check_prerequisites
    update_environment
    stop_existing_processes
    check_database
    start_backend
    check_frontend
    start_monitoring
    create_backup_script
    
    # Wait a moment for everything to stabilize
    sleep 10
    
    echo ""
    show_application_status
    
    print_success "¡Aplicación Tuchanga iniciada exitosamente!"
    echo ""
    print_status "Tu aplicación está disponible en:"
    echo "🌐 https://$DOMAIN"
    echo ""
    print_status "Comandos útiles:"
    echo "- Ver estado: ./check-status.sh"
    echo "- Ver logs: ./show-logs.sh"
    echo "- Reiniciar: ./restart-services.sh"
    echo "- Detener: ./stop-services.sh"
    echo ""
}

# Run main function
main "$@"
