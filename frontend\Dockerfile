# Etapa 1: Build del proyecto con Node 22 Alpine (smaller image)
FROM node:22-alpine AS builder

WORKDIR /app

# Copy package files
COPY package*.json ./

# Configure npm for better performance and install dependencies with memory constraints
RUN npm config set maxsockets 1 && \
    npm config set progress false && \
    npm ci --no-audit --no-fund --prefer-offline

# Copy source code and build with memory constraints
COPY . .

# Build with limited resources to prevent hanging
RUN NODE_OPTIONS="--max-old-space-size=1024" npm run build

# Etapa 2: Servir el build con nginx (much smaller than Node.js)
FROM nginx:alpine

# Copy built files from builder stage
COPY --from=builder /app/dist /usr/share/nginx/html

# Copy nginx configuration
COPY nginx.conf /etc/nginx/conf.d/default.conf

EXPOSE 80 443

CMD ["nginx", "-g", "daemon off;"]
