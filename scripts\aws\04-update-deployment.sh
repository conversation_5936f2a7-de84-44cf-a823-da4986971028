#!/bin/bash

# TuChanga - Update Deployment Script
# Updates backend, frontend, or both with database migrations

set -e

# Configuration
AWS_REGION="sa-east-1"
PROJECT_NAME="tuchanga"
AWS_ACCOUNT_ID=$(aws sts get-caller-identity --query Account --output text)

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to show usage
show_usage() {
    echo "Usage: $0 [backend|frontend|both]"
    echo ""
    echo "Updates TuChanga deployment components"
    echo ""
    echo "Options:"
    echo "  backend    Update only backend service (includes DB migrations)"
    echo "  frontend   Update only frontend service"
    echo "  both       Update both backend and frontend services"
    echo "  -h, --help Show this help message"
    echo ""
    echo "Examples:"
    echo "  $0 backend   # Update backend with DB migrations"
    echo "  $0 frontend  # Update frontend only"
    echo "  $0 both      # Update everything"
    echo "  $0           # Interactive mode - will prompt for choice"
}

# Function to run database migrations
run_migrations() {
    echo -e "${YELLOW}🗄️  Running database migrations...${NC}"
    
    # Get Aurora endpoint
    AURORA_ENDPOINT=$(aws rds describe-db-clusters --db-cluster-identifier $PROJECT_NAME-aurora-cluster --query 'DBClusters[0].Endpoint' --output text)
    
    if [ -z "$AURORA_ENDPOINT" ] || [ "$AURORA_ENDPOINT" = "None" ]; then
        echo -e "${RED}❌ Aurora cluster not found${NC}"
        return 1
    fi
    
    # Get database credentials
    DB_PASSWORD=$(aws secretsmanager get-secret-value --secret-id "$PROJECT_NAME/db-password" --query 'SecretString' --output text)
    
    # Create a temporary migration task
    echo "Creating migration task..."
    
    # Create migration task definition
    cat > migration-task-definition.json << EOF
{
  "family": "$PROJECT_NAME-migration",
  "networkMode": "awsvpc",
  "requiresCompatibilities": ["FARGATE"],
  "cpu": "256",
  "memory": "512",
  "executionRoleArn": "arn:aws:iam::$AWS_ACCOUNT_ID:role/$PROJECT_NAME-ecs-execution-role",
  "taskRoleArn": "arn:aws:iam::$AWS_ACCOUNT_ID:role/$PROJECT_NAME-ecs-task-role",
  "containerDefinitions": [
    {
      "name": "$PROJECT_NAME-migration",
      "image": "$AWS_ACCOUNT_ID.dkr.ecr.$AWS_REGION.amazonaws.com/$PROJECT_NAME-backend:latest",
      "essential": true,
      "environment": [
        {
          "name": "NODE_ENV",
          "value": "production"
        },
        {
          "name": "DB_HOST",
          "value": "$AURORA_ENDPOINT"
        },
        {
          "name": "DB_PORT",
          "value": "5432"
        },
        {
          "name": "DB_USERNAME",
          "value": "postgres"
        },
        {
          "name": "DB_DATABASE",
          "value": "tuchanga"
        }
      ],
      "secrets": [
        {
          "name": "DB_PASSWORD",
          "valueFrom": "arn:aws:secretsmanager:$AWS_REGION:$AWS_ACCOUNT_ID:secret:$PROJECT_NAME/db-password"
        },
        {
          "name": "DATABASE_URL",
          "valueFrom": "arn:aws:secretsmanager:$AWS_REGION:$AWS_ACCOUNT_ID:secret:$PROJECT_NAME/database-url"
        }
      ],
      "command": ["npm", "run", "migration:run"],
      "logConfiguration": {
        "logDriver": "awslogs",
        "options": {
          "awslogs-group": "/ecs/$PROJECT_NAME-backend",
          "awslogs-region": "$AWS_REGION",
          "awslogs-stream-prefix": "migration"
        }
      }
    }
  ]
}
EOF

    # Register migration task definition
    MIGRATION_TASK_DEF_ARN=$(aws ecs register-task-definition \
        --cli-input-json file://migration-task-definition.json \
        --query 'taskDefinition.taskDefinitionArn' \
        --output text)
    
    # Get VPC and subnet information
    VPC_ID=$(aws ec2 describe-vpcs --filters "Name=tag:Name,Values=$PROJECT_NAME-vpc" --query 'Vpcs[0].VpcId' --output text)
    PRIVATE_SUBNET_1_ID=$(aws ec2 describe-subnets --filters "Name=vpc-id,Values=$VPC_ID" "Name=tag:Name,Values=$PROJECT_NAME-private-1" --query 'Subnets[0].SubnetId' --output text)
    PRIVATE_SUBNET_2_ID=$(aws ec2 describe-subnets --filters "Name=vpc-id,Values=$VPC_ID" "Name=tag:Name,Values=$PROJECT_NAME-private-2" --query 'Subnets[0].SubnetId' --output text)
    ECS_SG_ID=$(aws ec2 describe-security-groups --filters "Name=group-name,Values=$PROJECT_NAME-ecs-sg" "Name=vpc-id,Values=$VPC_ID" --query 'SecurityGroups[0].GroupId' --output text)
    
    # Run migration task
    TASK_ARN=$(aws ecs run-task \
        --cluster "$PROJECT_NAME-cluster" \
        --task-definition $MIGRATION_TASK_DEF_ARN \
        --launch-type FARGATE \
        --network-configuration "awsvpcConfiguration={subnets=[$PRIVATE_SUBNET_1_ID,$PRIVATE_SUBNET_2_ID],securityGroups=[$ECS_SG_ID],assignPublicIp=DISABLED}" \
        --query 'tasks[0].taskArn' \
        --output text)
    
    echo "Waiting for migration task to complete..."
    aws ecs wait tasks-stopped --cluster "$PROJECT_NAME-cluster" --tasks $TASK_ARN
    
    # Check if migration was successful
    EXIT_CODE=$(aws ecs describe-tasks --cluster "$PROJECT_NAME-cluster" --tasks $TASK_ARN --query 'tasks[0].containers[0].exitCode' --output text)
    
    if [ "$EXIT_CODE" = "0" ]; then
        echo -e "${GREEN}✅ Database migrations completed successfully${NC}"
    else
        echo -e "${RED}❌ Database migrations failed with exit code: $EXIT_CODE${NC}"
        echo "Check CloudWatch logs for details: /ecs/$PROJECT_NAME-backend"
        return 1
    fi
    
    # Clean up
    rm -f migration-task-definition.json
}

# Function to update backend
update_backend() {
    echo -e "${YELLOW}🏗️  Updating backend service...${NC}"
    
    # Build and push new backend image
    cd ../../backend
    
    # Create new tag with timestamp
    TIMESTAMP=$(date +%Y%m%d_%H%M%S)
    NEW_TAG="$TIMESTAMP"
    
    echo "Building backend image with tag: $NEW_TAG"
    docker build -f Dockerfile.ecs -t $PROJECT_NAME-backend:$NEW_TAG .
    docker tag $PROJECT_NAME-backend:$NEW_TAG $AWS_ACCOUNT_ID.dkr.ecr.$AWS_REGION.amazonaws.com/$PROJECT_NAME-backend:$NEW_TAG
    docker tag $PROJECT_NAME-backend:$NEW_TAG $AWS_ACCOUNT_ID.dkr.ecr.$AWS_REGION.amazonaws.com/$PROJECT_NAME-backend:latest
    
    # Login to ECR
    aws ecr get-login-password --region $AWS_REGION | docker login --username AWS --password-stdin $AWS_ACCOUNT_ID.dkr.ecr.$AWS_REGION.amazonaws.com
    
    echo "Pushing backend image..."
    docker push $AWS_ACCOUNT_ID.dkr.ecr.$AWS_REGION.amazonaws.com/$PROJECT_NAME-backend:$NEW_TAG
    docker push $AWS_ACCOUNT_ID.dkr.ecr.$AWS_REGION.amazonaws.com/$PROJECT_NAME-backend:latest
    
    cd ../scripts/aws
    
    # Run database migrations first
    if ! run_migrations; then
        echo -e "${RED}❌ Migrations failed. Aborting backend update.${NC}"
        return 1
    fi
    
    # Update service with new image
    echo "Updating backend service..."
    aws ecs update-service \
        --cluster "$PROJECT_NAME-cluster" \
        --service "$PROJECT_NAME-backend-service" \
        --force-new-deployment
    
    echo "Waiting for backend service to stabilize..."
    aws ecs wait services-stable --cluster "$PROJECT_NAME-cluster" --services "$PROJECT_NAME-backend-service"
    
    echo -e "${GREEN}✅ Backend service updated successfully${NC}"
}

# Function to update frontend
update_frontend() {
    echo -e "${YELLOW}🎨 Updating frontend service...${NC}"
    
    # Build and push new frontend image
    cd ../../frontend
    
    # Create new tag with timestamp
    TIMESTAMP=$(date +%Y%m%d_%H%M%S)
    NEW_TAG="$TIMESTAMP"
    
    echo "Building frontend image with tag: $NEW_TAG"
    docker build -f Dockerfile.ecs -t $PROJECT_NAME-frontend:$NEW_TAG .
    docker tag $PROJECT_NAME-frontend:$NEW_TAG $AWS_ACCOUNT_ID.dkr.ecr.$AWS_REGION.amazonaws.com/$PROJECT_NAME-frontend:$NEW_TAG
    docker tag $PROJECT_NAME-frontend:$NEW_TAG $AWS_ACCOUNT_ID.dkr.ecr.$AWS_REGION.amazonaws.com/$PROJECT_NAME-frontend:latest
    
    # Login to ECR
    aws ecr get-login-password --region $AWS_REGION | docker login --username AWS --password-stdin $AWS_ACCOUNT_ID.dkr.ecr.$AWS_REGION.amazonaws.com
    
    echo "Pushing frontend image..."
    docker push $AWS_ACCOUNT_ID.dkr.ecr.$AWS_REGION.amazonaws.com/$PROJECT_NAME-frontend:$NEW_TAG
    docker push $AWS_ACCOUNT_ID.dkr.ecr.$AWS_REGION.amazonaws.com/$PROJECT_NAME-frontend:latest
    
    cd ../scripts/aws
    
    # Update service with new image
    echo "Updating frontend service..."
    aws ecs update-service \
        --cluster "$PROJECT_NAME-cluster" \
        --service "$PROJECT_NAME-frontend-service" \
        --force-new-deployment
    
    echo "Waiting for frontend service to stabilize..."
    aws ecs wait services-stable --cluster "$PROJECT_NAME-cluster" --services "$PROJECT_NAME-frontend-service"
    
    echo -e "${GREEN}✅ Frontend service updated successfully${NC}"
}

# Parse arguments
COMPONENT=""
if [ "$1" = "-h" ] || [ "$1" = "--help" ]; then
    show_usage
    exit 0
elif [ -n "$1" ]; then
    COMPONENT="$1"
fi

# Interactive mode if no component specified
if [ -z "$COMPONENT" ]; then
    echo -e "${YELLOW}🔄 TuChanga Update Deployment${NC}"
    echo ""
    echo "What would you like to update?"
    echo "1) Backend (includes database migrations)"
    echo "2) Frontend"
    echo "3) Both backend and frontend"
    echo "4) Exit"
    echo ""
    read -p "Enter your choice (1-4): " choice
    
    case $choice in
        1) COMPONENT="backend" ;;
        2) COMPONENT="frontend" ;;
        3) COMPONENT="both" ;;
        4) echo "Exiting..."; exit 0 ;;
        *) echo -e "${RED}❌ Invalid choice${NC}"; exit 1 ;;
    esac
fi

# Validate component
case $COMPONENT in
    "backend"|"frontend"|"both") ;;
    *) echo -e "${RED}❌ Invalid component: $COMPONENT${NC}"; show_usage; exit 1 ;;
esac

echo -e "${GREEN}🚀 Starting TuChanga deployment update${NC}"
echo -e "${BLUE}📦 Component: $COMPONENT${NC}"

# Execute updates based on component
case $COMPONENT in
    "backend")
        update_backend
        ;;
    "frontend")
        update_frontend
        ;;
    "both")
        update_backend
        update_frontend
        ;;
esac

# Get ALB DNS for final message
ALB_DNS=$(aws elbv2 describe-load-balancers --query "LoadBalancers[?contains(LoadBalancerName, '$PROJECT_NAME')].DNSName" --output text)

echo -e "${GREEN}🎉 Deployment update completed successfully!${NC}"
echo -e "${BLUE}📋 Summary:${NC}"
echo "Updated: $COMPONENT"
echo "Application URL: http://$ALB_DNS"
echo ""
echo -e "${YELLOW}💡 Tip: Check CloudWatch logs if you encounter any issues${NC}"
