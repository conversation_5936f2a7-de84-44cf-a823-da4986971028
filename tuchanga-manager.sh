#!/bin/bash

# Tuchanga Platform Manager
# Main script to manage your Tuchanga deployment

set -e

echo "🚀 Tuchanga Platform Manager"
echo "============================"
echo ""

# Function to show menu
show_menu() {
    echo "📋 Available actions:"
    echo ""
    echo "🔍 DIAGNOSIS & MONITORING:"
    echo "   1) Full system diagnosis"
    echo "   2) Check API configuration"
    echo "   3) Check container status"
    echo ""
    echo "🚀 CONTAINER MANAGEMENT:"
    echo "   4) Start containers (step-by-step)"
    echo "   5) Stop containers (gracefully)"
    echo "   6) Restart all containers"
    echo "   7) View container logs"
    echo ""
    echo "🔧 FIXES & SETUP:"
    echo "   8) Auto-fix deployment issues"
    echo "   9) Setup HTTPS certificates"
    echo "   10) Reset and rebuild everything"
    echo ""
    echo "📊 MONITORING:"
    echo "   11) Show resource usage"
    echo "   12) Monitor logs in real-time"
    echo ""
    echo "❌ EXIT:"
    echo "   0) Exit"
    echo ""
}

# Function to wait for user input
wait_for_input() {
    echo ""
    read -p "Press Enter to continue..."
    echo ""
}

# Function to show container status
show_container_status() {
    echo "📦 Container Status:"
    echo "-------------------"
    if docker ps --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}" --filter "name=tuchanga" | grep -q "tuchanga"; then
        docker ps --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}" --filter "name=tuchanga"
    else
        echo "No Tuchanga containers running"
    fi
    echo ""
}

# Function to show resource usage
show_resources() {
    echo "📊 System Resources:"
    echo "-------------------"
    echo "Memory usage:"
    free -h | head -2
    echo ""
    echo "CPU load:"
    uptime
    echo ""
    echo "Disk usage:"
    df -h / | tail -1
    echo ""
    
    if docker ps --filter "name=tuchanga" --format "{{.Names}}" | grep -q "tuchanga"; then
        echo "Docker container stats:"
        timeout 3 docker stats --no-stream --format "table {{.Name}}\t{{.CPUPerc}}\t{{.MemUsage}}" --filter "name=tuchanga" 2>/dev/null || echo "Could not get container stats"
    fi
    echo ""
}

# Main loop
while true; do
    clear
    echo "🚀 Tuchanga Platform Manager"
    echo "============================"
    echo ""
    
    # Show current status
    show_container_status
    
    # Show menu
    show_menu
    
    # Get user choice
    read -p "Choose an action (0-12): " choice
    echo ""
    
    case $choice in
        1)
            echo "🔍 Running full system diagnosis..."
            ./diagnose-deployment.sh
            wait_for_input
            ;;
        2)
            echo "🔍 Checking API configuration..."
            ./check-api-config.sh
            wait_for_input
            ;;
        3)
            echo "📦 Container status:"
            show_container_status
            show_resources
            wait_for_input
            ;;
        4)
            echo "🚀 Starting containers step-by-step..."
            ./start-containers-step-by-step.sh
            wait_for_input
            ;;
        5)
            echo "🛑 Stopping containers gracefully..."
            ./stop-containers-gracefully.sh
            wait_for_input
            ;;
        6)
            echo "🔄 Restarting all containers..."
            echo "Stopping containers..."
            ./stop-containers-gracefully.sh
            echo ""
            echo "Starting containers..."
            ./start-containers-step-by-step.sh
            wait_for_input
            ;;
        7)
            echo "📝 Container logs:"
            echo "=================="
            echo "Choose container:"
            echo "1) All containers"
            echo "2) Frontend only"
            echo "3) Backend only"
            echo "4) Database only"
            read -p "Choice (1-4): " log_choice
            
            case $log_choice in
                1) docker-compose logs --tail=50 ;;
                2) docker-compose logs --tail=50 frontend ;;
                3) docker-compose logs --tail=50 backend ;;
                4) docker-compose logs --tail=50 postgres ;;
                *) echo "Invalid choice" ;;
            esac
            wait_for_input
            ;;
        8)
            echo "🔧 Running auto-fix for deployment issues..."
            ./fix-deployment.sh
            wait_for_input
            ;;
        9)
            echo "🔐 Setting up HTTPS certificates..."
            ./setup-https.sh
            wait_for_input
            ;;
        10)
            echo "⚠️  This will stop all containers and rebuild everything!"
            read -p "Are you sure? (y/N): " confirm
            if [[ $confirm =~ ^[Yy]$ ]]; then
                echo "🛑 Stopping containers..."
                docker-compose down --remove-orphans
                
                echo "🧹 Cleaning Docker resources..."
                docker system prune -f
                
                echo "🔨 Rebuilding containers..."
                docker-compose build --no-cache
                
                echo "🚀 Starting containers..."
                ./start-containers-step-by-step.sh
            else
                echo "Operation cancelled"
            fi
            wait_for_input
            ;;
        11)
            echo "📊 System resource usage:"
            show_resources
            wait_for_input
            ;;
        12)
            echo "📊 Monitoring logs in real-time..."
            echo "Press Ctrl+C to stop monitoring"
            echo ""
            docker-compose logs -f
            ;;
        0)
            echo "👋 Goodbye!"
            exit 0
            ;;
        *)
            echo "❌ Invalid choice. Please select 0-12."
            sleep 2
            ;;
    esac
done
