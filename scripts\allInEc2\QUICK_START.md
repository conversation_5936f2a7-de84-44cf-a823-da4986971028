# 🚀 Tuchanga EC2 - Guía de Inicio Rápido (Despliegue Nativo)

## ⚡ Instalación en 5 Minutos

### 1. Conectar a tu EC2
```bash
ssh -i tu-clave.pem ubuntu@tu-ec2-ip
```

### 2. Clonar el repositorio
```bash
# Clonar repositorio
git clone https://github.com/tu-usuario/tuchanga.git
cd tuchanga/scripts/allInEc2
```

### 3. Ejecutar instalación automática
```bash
# Hacer ejecutables los scripts
chmod +x *.sh

# Opcional: Probar scripts
./test-scripts.sh

# Verificar requisitos (opcional)
./pre-install-check.sh tu-dominio.com

# Instalación completa
./main.sh
```

Selecciona **opción 1** y proporciona:
- **Dominio**: `midominio.com`
- **Email**: `<EMAIL>`

**✅ Mejoras del Despliegue Nativo**:
- Sin Docker: Evita problemas de permisos completamente
- PM2: Gestión profesional de procesos Node.js
- PostgreSQL nativo: Mejor rendimiento y simplicidad
- El script detecta automáticamente el directorio del proyecto
- Continúa automáticamente si solo hay warnings (como CPU de 1 core)
- Solo se detiene en errores críticos

## 📋 Prerrequisitos Mínimos

### EC2 Instance
- **OS**: Ubuntu 24 LTS
- **Tipo**: t3.small (2 vCPU, 2 GB RAM)
- **Storage**: 20 GB SSD
- **Puertos**: 22, 80, 443, 3000

### DNS Configuration
```
A    midominio.com        → IP_DE_TU_EC2
A    www.midominio.com    → IP_DE_TU_EC2
A    api.midominio.com    → IP_DE_TU_EC2
```

### Repositorio
- Repositorio clonado localmente
- Ejecutar desde `tuchanga/scripts/allInEc2/`

## 🎯 URLs Finales

Después de la instalación:
- **Frontend**: `https://midominio.com`
- **API**: `https://api.midominio.com`
- **Admin**: SSH a tu EC2

## 🔧 Comandos Útiles

```bash
# Ver estado completo
./check-status.sh

# Ver logs
./show-logs.sh

# Reiniciar servicios
./restart-services.sh

# Detener servicios
./stop-services.sh

# Menú principal
./main.sh
```

## 🚨 Solución Rápida de Problemas

### Error de PostgreSQL
```bash
# Verificar estado
sudo systemctl status postgresql

# Reiniciar servicio
sudo systemctl restart postgresql
```

### Certificados SSL no funcionan
```bash
# Verificar DNS
dig midominio.com

# Regenerar certificados
./setup-ssl.sh midominio.com <EMAIL>
```

### Backend no responde
```bash
# Ver logs de PM2
pm2 logs tuchanga-backend

# Reiniciar con PM2
pm2 restart tuchanga-backend

# O usar script
./restart-services.sh backend
```

### Base de datos no conecta
```bash
# Ver estado de PostgreSQL
sudo systemctl status postgresql

# Reiniciar PostgreSQL
sudo systemctl restart postgresql

# O usar script
./restart-services.sh database
```

## 📊 Verificación Final

Después de la instalación, verifica:

1. **Estado de servicios**:
   ```bash
   ./check-status.sh
   ```

2. **Acceso web**:
   - Abre `https://midominio.com`
   - Verifica que carga la aplicación

3. **API funcionando**:
   ```bash
   curl https://api.midominio.com/health
   ```

## 🔄 Actualizaciones

Para actualizar la aplicación:
```bash
cd tuchanga/scripts/allInEc2
./setup-repository.sh
./restart-services.sh all
```

## 📞 Soporte

Si algo no funciona:
1. Ejecuta `./check-status.sh`
2. Revisa `./show-logs.sh errors`
3. Verifica que el DNS esté configurado
4. Asegúrate de que los puertos estén abiertos

---

¡Tu aplicación Tuchanga estará lista en minutos! 🎉
