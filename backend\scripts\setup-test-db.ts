import { DataSource } from 'typeorm';
import { ConfigService } from '@nestjs/config';

async function setupTestDatabase() {
  const configService = new ConfigService();
  
  console.log('🧪 Setting up test database...');
  
  // Connect to PostgreSQL server (not specific database)
  const adminDataSource = new DataSource({
    type: 'postgres',
    host: configService.get('DB_HOST', 'localhost'),
    port: configService.get('DB_PORT', 5432),
    username: configService.get('DB_USERNAME', 'postgres'),
    password: configService.get('DB_PASSWORD', 'postgres123'),
    database: 'postgres', // Connect to default postgres database
  });

  try {
    await adminDataSource.initialize();
    console.log('📦 Connected to PostgreSQL server');

    // Check if test database exists
    const result = await adminDataSource.query(
      "SELECT 1 FROM pg_database WHERE datname = 'tuchanga_test'"
    );

    if (result.length === 0) {
      // Create test database
      await adminDataSource.query('CREATE DATABASE tuchanga_test');
      console.log('✅ Test database created: tuchanga_test');
    } else {
      console.log('✅ Test database already exists: tuchanga_test');
    }

  } catch (error) {
    console.error('❌ Error setting up test database:', error);
    throw error;
  } finally {
    await adminDataSource.destroy();
  }
}

// Run setup if this file is executed directly
if (require.main === module) {
  setupTestDatabase()
    .then(() => {
      console.log('🎉 Test database setup completed');
      process.exit(0);
    })
    .catch((error) => {
      console.error('💥 Test database setup failed:', error);
      process.exit(1);
    });
}

export { setupTestDatabase };
