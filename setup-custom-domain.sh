#!/bin/bash

# Setup custom domain for Tuchanga Job Platform
# This script configures SSL certificates and updates configuration for your custom domain

set -e

echo "🌐 Setting up custom domain for Tuchanga Job Platform..."

# Get domain from user
read -p "Enter your domain name (e.g., midominio.com): " DOMAIN

if [[ -z "$DOMAIN" ]]; then
    echo "❌ Domain name is required"
    exit 1
fi

echo "📋 Domain configuration:"
echo "   Main domain: $DOMAIN"
echo "   WWW domain: www.$DOMAIN"
echo "   API domain: api.$DOMAIN"
echo ""

# Validate domain format
if [[ ! "$DOMAIN" =~ ^[a-zA-Z0-9][a-zA-Z0-9-]{1,61}[a-zA-Z0-9]\.[a-zA-Z]{2,}$ ]]; then
    echo "⚠️  Warning: Domain format might be invalid"
    read -p "Continue anyway? (y/n): " confirm
    if [[ $confirm != "y" ]]; then
        echo "❌ Aborted"
        exit 1
    fi
fi

# Update nginx configuration
echo "🔧 Updating nginx configuration..."
sed -i "s/midominio\.com/$DOMAIN/g" frontend/nginx.conf

# Create SSL directory
SSL_DIR="./ssl"
mkdir -p $SSL_DIR

echo ""
echo "📋 Choose SSL certificate option:"
echo "1. Self-signed certificate (Quick setup, browser warnings)"
echo "2. Let's Encrypt certificate (Recommended, requires domain DNS setup)"
echo "3. Manual certificate (Provide your own certificates)"

read -p "Enter your choice (1-3): " choice

case $choice in
    1)
        echo "🔧 Generating self-signed certificate for $DOMAIN..."
        
        # Generate private key
        openssl genrsa -out $SSL_DIR/key.pem 2048
        
        # Generate certificate with SAN for multiple domains
        openssl req -new -x509 -key $SSL_DIR/key.pem -out $SSL_DIR/cert.pem -days 365 \
            -subj "/C=AR/ST=Cordoba/L=Cordoba/O=Tuchanga/CN=$DOMAIN" \
            -addext "subjectAltName=DNS:$DOMAIN,DNS:www.$DOMAIN,DNS:api.$DOMAIN"
        
        # Set permissions
        chmod 644 $SSL_DIR/cert.pem
        chmod 600 $SSL_DIR/key.pem
        
        echo "✅ Self-signed certificate generated!"
        echo "⚠️  Note: Browsers will show security warnings for self-signed certificates"
        ;;
        
    2)
        echo "🔧 Setting up Let's Encrypt certificate for $DOMAIN..."
        
        # Check if certbot is installed
        if ! command -v certbot &> /dev/null; then
            echo "📦 Installing certbot..."
            
            # Detect OS and install certbot
            if [[ -f /etc/os-release ]]; then
                source /etc/os-release
                OS_ID=$ID
            else
                OS_ID="unknown"
            fi
            
            case $OS_ID in
                "amzn")
                    sudo yum update -y
                    sudo yum install -y python3-pip
                    sudo pip3 install certbot
                    ;;
                "ubuntu"|"debian")
                    sudo apt update
                    sudo apt install -y certbot
                    ;;
                "centos"|"rhel"|"fedora")
                    sudo yum install -y epel-release
                    sudo yum install -y certbot
                    ;;
                *)
                    echo "❌ Unsupported OS: $OS_ID"
                    echo "Please install certbot manually and run this script again"
                    exit 1
                    ;;
            esac
        fi
        
        echo "🌐 Obtaining Let's Encrypt certificate for $DOMAIN..."
        echo "⚠️  Make sure your DNS records point to this server:"
        echo "   A record: $DOMAIN -> $(curl -s http://***************/latest/meta-data/public-ipv4 2>/dev/null || echo 'YOUR_SERVER_IP')"
        echo "   A record: www.$DOMAIN -> $(curl -s http://***************/latest/meta-data/public-ipv4 2>/dev/null || echo 'YOUR_SERVER_IP')"
        echo "   A record: api.$DOMAIN -> $(curl -s http://***************/latest/meta-data/public-ipv4 2>/dev/null || echo 'YOUR_SERVER_IP')"
        echo "⚠️  Port 80 must be accessible from the internet"
        echo ""
        
        read -p "Continue with Let's Encrypt setup? (y/n): " confirm
        if [[ $confirm != "y" ]]; then
            echo "❌ Aborted"
            exit 1
        fi
        
        # Get email for Let's Encrypt
        read -p "Enter email for Let's Encrypt notifications: " EMAIL
        if [[ -z "$EMAIL" ]]; then
            echo "❌ Email is required for Let's Encrypt"
            exit 1
        fi
        
        # Stop any running containers to free port 80
        echo "🛑 Stopping containers to free port 80..."
        docker-compose down || true
        
        # Obtain certificate for all domains
        sudo certbot certonly --standalone \
            --email $EMAIL \
            --agree-tos \
            --no-eff-email \
            -d $DOMAIN \
            -d www.$DOMAIN \
            -d api.$DOMAIN
        
        # Copy certificates to our SSL directory
        sudo cp /etc/letsencrypt/live/$DOMAIN/fullchain.pem $SSL_DIR/cert.pem
        sudo cp /etc/letsencrypt/live/$DOMAIN/privkey.pem $SSL_DIR/key.pem
        
        # Fix permissions
        sudo chown $(whoami):$(whoami) $SSL_DIR/cert.pem $SSL_DIR/key.pem
        
        echo "✅ Let's Encrypt certificate obtained!"
        ;;
        
    3)
        echo "📁 Manual certificate setup..."
        echo "Please place your certificate files in the ssl/ directory:"
        echo "   ssl/cert.pem - Your certificate file"
        echo "   ssl/key.pem  - Your private key file"
        echo ""
        echo "Make sure your certificate includes all domains:"
        echo "   - $DOMAIN"
        echo "   - www.$DOMAIN" 
        echo "   - api.$DOMAIN"
        
        read -p "Press Enter when certificates are ready..."
        
        if [[ ! -f "$SSL_DIR/cert.pem" || ! -f "$SSL_DIR/key.pem" ]]; then
            echo "❌ Certificate files not found in ssl/ directory"
            exit 1
        fi
        
        echo "✅ Manual certificates configured!"
        ;;
        
    *)
        echo "❌ Invalid choice"
        exit 1
        ;;
esac

# Update environment variables if needed
if [[ -f ".env" ]]; then
    echo "🔧 Updating environment variables..."
    
    # Update CORS_ORIGIN
    if grep -q "CORS_ORIGIN=" .env; then
        sed -i "s|CORS_ORIGIN=.*|CORS_ORIGIN=https://$DOMAIN,https://www.$DOMAIN|" .env
    else
        echo "CORS_ORIGIN=https://$DOMAIN,https://www.$DOMAIN" >> .env
    fi
    
    echo "✅ Environment variables updated"
fi

echo ""
echo "🚀 Starting application with custom domain..."
docker-compose up -d --build

# Wait for containers to be ready
echo "⏳ Waiting for containers to start..."
sleep 15

# Check container status
echo "📊 Container status:"
docker-compose ps

echo ""
echo "🎉 Custom domain setup complete!"
echo ""
echo "🌐 Your application URLs:"
echo "   Main site: https://$DOMAIN"
echo "   WWW site:  https://www.$DOMAIN"
echo "   API:       https://api.$DOMAIN"
echo ""
echo "📋 DNS Configuration Required:"
echo "   Add these A records in your domain's DNS settings:"
echo "   ┌─────────────┬──────┬─────────────────────────────────┐"
echo "   │ Name        │ Type │ Value                           │"
echo "   ├─────────────┼──────┼─────────────────────────────────┤"
echo "   │ @           │ A    │ $(curl -s http://***************/latest/meta-data/public-ipv4 2>/dev/null || echo 'YOUR_SERVER_IP') │"
echo "   │ www         │ A    │ $(curl -s http://***************/latest/meta-data/public-ipv4 2>/dev/null || echo 'YOUR_SERVER_IP') │"
echo "   │ api         │ A    │ $(curl -s http://***************/latest/meta-data/public-ipv4 2>/dev/null || echo 'YOUR_SERVER_IP') │"
echo "   └─────────────┴──────┴─────────────────────────────────┘"
echo ""

if [[ $choice == "2" ]]; then
    echo "🔄 Let's Encrypt certificate renewal:"
    echo "   Certificates expire in 90 days"
    echo "   Set up auto-renewal with: sudo crontab -e"
    echo "   Add: 0 12 * * * /usr/bin/certbot renew --quiet && docker-compose restart frontend"
    echo ""
fi

echo "✅ Setup complete!"
echo ""
echo "📝 Next steps:"
echo "   1. Configure DNS records as shown above"
echo "   2. Wait for DNS propagation (5-30 minutes)"
echo "   3. Test your domains:"
echo "      - curl -I https://$DOMAIN"
echo "      - curl -I https://api.$DOMAIN"
echo ""
