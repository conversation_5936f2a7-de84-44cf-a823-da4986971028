# Tuchanga EC2 Ubuntu 24 LTS - Guía de Despliegue Nativo

Esta carpeta contiene scripts automatizados para desplegar completamente la aplicación Tuchanga de forma nativa en una instancia EC2 con Ubuntu 24 LTS (sin Docker).

## 📋 Prerrequisitos

### 1. Instancia EC2
- **Sistema Operativo**: Ubuntu 24 LTS
- **Tipo de instancia**: <PERSON><PERSON><PERSON> t3.small (2 vCPU, 2 GB RAM)
- **Almacenamiento**: <PERSON><PERSON>imo 20 GB SSD
- **Puertos abiertos**: 22 (SSH), 80 (HTTP), 443 (HTTPS), 3000 (Backend)

### 2. <PERSON><PERSON><PERSON> y DNS
- Dominio registrado (ej: `midominio.com`)
- Registros DNS configurados apuntando a la IP de tu EC2:
  ```
  A    midominio.com        → IP_DE_TU_EC2
  A    www.midominio.com    → IP_DE_TU_EC2
  A    api.midominio.com    → IP_DE_TU_EC2
  ```

### 3. Repositorio GitHub
- Repositorio clonado localmente con el código de Tuchanga
- Los scripts se ejecutan desde dentro del repositorio

### 4. Acceso SSH a EC2
- Clave SSH para conectar a la instancia EC2
- Usuario con permisos sudo (generalmente `ubuntu`)

## 🚀 Instalación Rápida (Un Solo Comando)

```bash
# Conectar a tu EC2
ssh -i tu-clave.pem ubuntu@tu-ec2-ip

# Clonar el repositorio
git clone https://github.com/tu-usuario/tuchanga.git
cd tuchanga/scripts/allInEc2

# Hacer ejecutables los scripts
chmod +x *.sh

# Opcional: Probar scripts antes de la instalación
./test-scripts.sh

# Ejecutar instalación completa
./main.sh
```

Selecciona la opción **1** (Instalación completa desde cero) y sigue las instrucciones.

**Notas importantes**:
- El script detecta automáticamente el directorio del proyecto
- No necesitas especificar la URL del repositorio (ya está clonado)
- Los warnings no detendrán la instalación, solo los errores críticos

## 📁 Scripts Incluidos

### Script Principal
- **`main.sh`**: Menú interactivo principal con todas las opciones

### Scripts de Configuración
- **`install-dependencies.sh`**: Instala todas las dependencias del sistema (Node.js, PostgreSQL, PM2, Nginx)
- **`setup-repository.sh`**: Actualiza el repositorio e instala dependencias
- **`setup-database.sh`**: Configura PostgreSQL nativo
- **`setup-application.sh`**: Construye y configura la aplicación con PM2
- **`setup-ssl.sh`**: Configura certificados SSL (Let's Encrypt o auto-firmados)
- **`setup-nginx.sh`**: Configura Nginx como proxy reverso
- **`start-application.sh`**: Inicia toda la aplicación

### Scripts de Gestión
- **`check-status.sh`**: Verifica el estado de todos los servicios
- **`restart-services.sh`**: Reinicia servicios específicos o todos
- **`stop-services.sh`**: Detiene servicios con página de mantenimiento
- **`show-logs.sh`**: Muestra logs de todos los servicios

### Scripts de Utilidades
- **`test-scripts.sh`**: Verifica la integridad de todos los scripts

## 🔧 Configuración Manual Paso a Paso

Si prefieres ejecutar cada paso manualmente:

### 1. Preparar el Sistema
```bash
# Actualizar sistema e instalar dependencias
./install-dependencies.sh

# Actualizar repositorio e instalar dependencias del proyecto
./setup-repository.sh
```

### 2. Configurar Servicios
```bash
# Configurar base de datos
./setup-database.sh tu-password-db

# Configurar aplicación
./setup-application.sh

# Configurar SSL
./setup-ssl.sh midominio.com <EMAIL>

# Configurar Nginx
./setup-nginx.sh midominio.com
```

### 3. Iniciar Aplicación
```bash
# Iniciar toda la aplicación
./start-application.sh midominio.com tu-password-db tu-jwt-secret
```

## 📁 Estructura del Proyecto

Los scripts detectan automáticamente la estructura del proyecto:

```
tuchanga/
├── backend/          # Código del backend
├── frontend/         # Código del frontend
├── scripts/
│   └── allInEc2/     # Scripts de despliegue (aquí estás)
└── docker-compose.yml
```

**Importante**: Ejecuta los scripts desde `tuchanga/scripts/allInEc2/`

## 🌐 URLs de Acceso

Una vez completada la instalación, tu aplicación estará disponible en:

- **Frontend**: `https://midominio.com`
- **Frontend (www)**: `https://www.midominio.com`
- **API Backend**: `https://api.midominio.com`
- **API (proxy)**: `https://midominio.com/api/`

## 📊 Gestión y Monitoreo

### Verificar Estado
```bash
./check-status.sh
```

### Ver Logs
```bash
# Logs interactivos
./show-logs.sh

# Logs específicos
./show-logs.sh backend
./show-logs.sh database
./show-logs.sh nginx
./show-logs.sh errors
```

### Reiniciar Servicios
```bash
# Reiniciar todo
./restart-services.sh all

# Reiniciar servicios específicos
./restart-services.sh nginx
./restart-services.sh backend
./restart-services.sh database
```

### Detener Servicios
```bash
# Detener todo (activa página de mantenimiento)
./stop-services.sh all

# Detener servicios específicos
./stop-services.sh nginx
./stop-services.sh containers
```

## 🔒 Seguridad

### Firewall
Los scripts configuran automáticamente UFW con las siguientes reglas:
- Puerto 22 (SSH)
- Puerto 80 (HTTP - redirige a HTTPS)
- Puerto 443 (HTTPS)
- Puerto 3000 (Backend - solo localhost)
- Puerto 5432 (PostgreSQL - solo localhost)

### SSL/TLS
- Certificados Let's Encrypt con renovación automática
- Configuración SSL moderna (TLS 1.2+)
- Headers de seguridad configurados
- Redirección automática HTTP → HTTPS

### Base de Datos
- PostgreSQL ejecutándose en contenedor Docker
- Acceso restringido solo desde localhost
- Backups automáticos diarios

## 🔄 Backups Automáticos

Los scripts configuran backups automáticos:
- **Base de datos**: Diario a las 2:00 AM
- **Archivos del proyecto**: Diario a las 2:00 AM
- **Retención**: 7 días
- **Ubicación**: `/opt/tuchanga-backups/`

## 🚨 Solución de Problemas

### Problemas Comunes

1. **Error de conexión a PostgreSQL**
   ```bash
   # Verificar estado del servicio
   sudo systemctl status postgresql

   # Reiniciar PostgreSQL
   sudo systemctl restart postgresql

   # Verificar configuración
   ./setup-database.sh tu-password
   ```

2. **Backend no inicia con PM2**
   ```bash
   # Verificar estado de PM2
   pm2 status

   # Ver logs del backend
   pm2 logs tuchanga-backend

   # Reiniciar backend
   pm2 restart tuchanga-backend

   # Reconstruir aplicación
   ./setup-application.sh
   ```

3. **Certificados SSL no funcionan**
   ```bash
   # Verificar DNS
   dig midominio.com

   # Regenerar certificados
   ./setup-ssl.sh midominio.com <EMAIL>
   ```

4. **Problemas de dependencias Node.js**
   ```bash
   # Verificar versión de Node.js
   node --version

   # Reinstalar dependencias
   cd /ruta/al/proyecto/backend
   npm ci

   # Reconstruir aplicación
   ./setup-application.sh
   ```

### Logs de Diagnóstico
```bash
# Estado completo del sistema
./check-status.sh

# Solo errores
./show-logs.sh errors

# Exportar todos los logs
./show-logs.sh export
```

## 📞 Soporte

Si encuentras problemas:

1. Ejecuta `./check-status.sh` para diagnóstico completo
2. Revisa los logs con `./show-logs.sh errors`
3. Verifica que el DNS esté configurado correctamente
4. Asegúrate de que los puertos estén abiertos en el Security Group de EC2

## 🔄 Actualizaciones

Para actualizar la aplicación:

```bash
# Actualizar código desde GitHub
./setup-repository.sh

# Reconstruir y reiniciar
./restart-services.sh all
```

## 📝 Notas Importantes

- **Tiempo de instalación**: 15-30 minutos dependiendo de la velocidad de internet
- **Recursos mínimos**: 2 GB RAM, 2 vCPU, 20 GB almacenamiento
- **Certificados SSL**: Se renuevan automáticamente cada 90 días
- **Monitoreo**: Health checks cada 5 minutos
- **Backups**: Automáticos diarios con retención de 7 días

---

¡Tu aplicación Tuchanga estará completamente operativa siguiendo esta guía! 🎉
